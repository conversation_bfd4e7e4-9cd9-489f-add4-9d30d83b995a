<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication - Barber Brothers Legacy</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d1810 100%);
            color: white;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .auth-container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 400px;
            width: 90%;
        }
        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #dc3545;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .status {
            margin: 1rem 0;
            padding: 1rem;
            border-radius: 8px;
            font-weight: bold;
        }
        .status.success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
        }
        .status.error {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid #dc3545;
            color: #dc3545;
        }
        .status.processing {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            color: #ffc107;
        }
        .redirect-info {
            margin-top: 1rem;
            font-size: 0.9rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <h2>🔐 Authentication</h2>
        <div class="spinner" id="spinner"></div>
        <div id="status" class="status processing">
            Processing authentication...
        </div>
        <div class="redirect-info" id="redirect-info">
            Please wait while we complete your sign-in.
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/10.14.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.14.1/firebase-auth-compat.js"></script>
    
    <!-- Firebase Configuration -->
    <script>
        // Firebase configuration - New Firebase project
        const firebaseConfig = {
            apiKey: "AIzaSyBLFm5Kmdw4yA6fHqB1YFgzrJPmzE46CNM",
            authDomain: "barberbrothers-45d06.firebaseapp.com",
            projectId: "barberbrothers-45d06",
            storageBucket: "barberbrothers-45d06.firebasestorage.app",
            messagingSenderId: "1064307847156",
            appId: "1:1064307847156:web:5eed1300bd16839804d3a1",
            measurementId: "G-MHG8DQY38J"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
    </script>

    <script>
        function updateStatus(message, type = 'processing') {
            const statusEl = document.getElementById('status');
            const spinnerEl = document.getElementById('spinner');
            
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            
            if (type !== 'processing') {
                spinnerEl.style.display = 'none';
            }
        }

        function updateRedirectInfo(message) {
            document.getElementById('redirect-info').textContent = message;
        }

        // Handle authentication callback
        function handleAuthCallback() {
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const error = urlParams.get('error');
            const state = urlParams.get('state');

            console.log('🔧 Auth callback received:', { code: !!code, error, state });

            if (error) {
                console.error('❌ OAuth error:', error);
                updateStatus(`Authentication failed: ${error}`, 'error');
                updateRedirectInfo('Please close this window and try again.');
                return;
            }

            if (code) {
                updateStatus('Authentication successful! Redirecting...', 'success');
                updateRedirectInfo('You will be redirected to the main site shortly.');
                
                // Redirect to main site after a short delay
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
            } else {
                // Check if user is already signed in via Firebase
                firebase.auth().onAuthStateChanged((user) => {
                    if (user) {
                        console.log('✅ User already authenticated:', user.email);
                        updateStatus('Already signed in! Redirecting...', 'success');
                        updateRedirectInfo('Redirecting to main site...');
                        
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 1500);
                    } else {
                        console.log('ℹ️ No authentication code or user found');
                        updateStatus('No authentication data found', 'error');
                        updateRedirectInfo('Please close this window and try signing in again.');
                    }
                });
            }
        }

        // Handle popup authentication (for popup flow)
        function handlePopupAuth() {
            try {
                // This will be called if this page is opened in a popup
                if (window.opener && !window.opener.closed) {
                    console.log('🔧 Popup authentication detected');
                    
                    firebase.auth().onAuthStateChanged((user) => {
                        if (user) {
                            console.log('✅ Popup auth successful:', user.email);
                            updateStatus('Authentication successful!', 'success');
                            updateRedirectInfo('Closing popup...');
                            
                            // Notify parent window and close popup
                            setTimeout(() => {
                                if (window.opener) {
                                    window.opener.postMessage({ 
                                        type: 'AUTH_SUCCESS', 
                                        user: {
                                            uid: user.uid,
                                            email: user.email,
                                            displayName: user.displayName
                                        }
                                    }, window.location.origin);
                                }
                                window.close();
                            }, 1000);
                        }
                    });
                }
            } catch (error) {
                console.error('❌ Popup auth error:', error);
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔧 Auth callback page loaded');
            console.log('📍 Current URL:', window.location.href);
            console.log('🔍 URL params:', window.location.search);
            
            // Handle both popup and redirect flows
            handlePopupAuth();
            handleAuthCallback();
        });

        // Handle messages from parent window (for popup flow)
        window.addEventListener('message', (event) => {
            if (event.origin !== window.location.origin) return;
            
            console.log('📨 Received message:', event.data);
            
            if (event.data.type === 'AUTH_REQUEST') {
                handleAuthCallback();
            }
        });
    </script>
</body>
</html>
