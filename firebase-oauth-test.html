<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase OAuth Diagnostic Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #121212;
            color: #fff;
        }
        .test-card {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .status-good { color: #4CAF50; }
        .status-bad { color: #f44336; }
        .status-warning { color: #ff9800; }
        .code-block {
            background: #000;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            border-left: 4px solid #D90000;
        }
        button {
            background: #D90000;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #b30000;
        }
        .error-details {
            background: #2d1b1b;
            border: 1px solid #f44336;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔥 Firebase OAuth Diagnostic Test</h1>
    
    <div class="test-card">
        <h3>📍 Current Environment</h3>
        <p><strong>URL:</strong> <span id="current-url"></span></p>
        <p><strong>Domain:</strong> <span id="current-domain"></span></p>
        <p><strong>Protocol:</strong> <span id="current-protocol"></span></p>
    </div>

    <div class="test-card">
        <h3>🔧 Firebase Configuration Test</h3>
        <p id="firebase-status">⏳ Checking Firebase...</p>
        <p id="auth-status">⏳ Checking Authentication...</p>
        <p id="domain-status">⏳ Checking Domain Authorization...</p>
    </div>

    <div class="test-card">
        <h3>🧪 OAuth Test</h3>
        <button onclick="testGoogleSignIn()">Test Google Sign-In</button>
        <button onclick="testPopupSignIn()">Test Popup Sign-In</button>
        <div id="oauth-result"></div>
    </div>

    <div class="test-card">
        <h3>❌ Error Details</h3>
        <div id="error-details" style="display: none;"></div>
    </div>

    <div class="test-card">
        <h3>🛠️ Firebase Console Fix</h3>
        <p>If you see domain authorization errors, add these domains to Firebase:</p>
        <div class="code-block">
1. Go to <a href="https://console.firebase.google.com/" target="_blank">Firebase Console</a><br>
2. Select your project: "barber-brothers-legacy"<br>
3. Go to Authentication → Settings → Authorized domains<br>
4. Add these domains:<br>
   • barberbrotherz.com<br>
   • www.barberbrotherz.com<br>
   • localhost (for testing)
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/10.14.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.14.1/firebase-auth-compat.js"></script>
    
    <script>
        // Firebase configuration - New Firebase project
        const firebaseConfig = {
            apiKey: "AIzaSyBLFm5Kmdw4yA6fHqB1YFgzrJPmzE46CNM",
            authDomain: "barberbrothers-45d06.firebaseapp.com",
            projectId: "barberbrothers-45d06",
            storageBucket: "barberbrothers-45d06.firebasestorage.app",
            messagingSenderId: "1064307847156",
            appId: "1:1064307847156:web:5eed1300bd16839804d3a1",
            measurementId: "G-MHG8DQY38J"
        };

        // Initialize Firebase
        let auth;
        try {
            firebase.initializeApp(firebaseConfig);
            auth = firebase.auth();
            console.log('✅ Firebase initialized successfully');
        } catch (error) {
            console.error('❌ Firebase initialization failed:', error);
        }

        // Update environment info
        function updateEnvironmentInfo() {
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('current-domain').textContent = window.location.hostname;
            document.getElementById('current-protocol').textContent = window.location.protocol;
        }

        // Test Firebase status
        function testFirebaseStatus() {
            try {
                if (typeof firebase !== 'undefined' && firebase.apps.length > 0) {
                    document.getElementById('firebase-status').innerHTML = '✅ <span class="status-good">Firebase loaded successfully</span>';
                    
                    if (auth) {
                        document.getElementById('auth-status').innerHTML = '✅ <span class="status-good">Firebase Auth initialized</span>';
                    } else {
                        document.getElementById('auth-status').innerHTML = '❌ <span class="status-bad">Firebase Auth failed to initialize</span>';
                    }
                } else {
                    document.getElementById('firebase-status').innerHTML = '❌ <span class="status-bad">Firebase not loaded</span>';
                    document.getElementById('auth-status').innerHTML = '❌ <span class="status-bad">Firebase Auth not available</span>';
                }
            } catch (error) {
                document.getElementById('firebase-status').innerHTML = '❌ <span class="status-bad">Firebase error: ' + error.message + '</span>';
                console.error('Firebase status check error:', error);
            }
        }

        // Test Google Sign-In
        async function testGoogleSignIn() {
            const resultDiv = document.getElementById('oauth-result');
            const errorDiv = document.getElementById('error-details');
            
            resultDiv.innerHTML = '⏳ Testing Google Sign-In...';
            errorDiv.style.display = 'none';
            
            try {
                const provider = new firebase.auth.GoogleAuthProvider();
                provider.addScope('email');
                provider.addScope('profile');
                
                console.log('🔧 Attempting Google sign-in...');
                const result = await auth.signInWithPopup(provider);
                
                resultDiv.innerHTML = `
                    <div class="status-good">
                        ✅ <strong>Success!</strong><br>
                        User: ${result.user.email}<br>
                        UID: ${result.user.uid}
                    </div>
                `;
                
                console.log('✅ Google sign-in successful:', result.user);
                
            } catch (error) {
                console.error('❌ Google sign-in error:', error);
                
                resultDiv.innerHTML = `<div class="status-bad">❌ <strong>Failed:</strong> ${error.code}</div>`;
                
                errorDiv.innerHTML = `
                    <div class="error-details">
                        <strong>Error Code:</strong> ${error.code}<br>
                        <strong>Error Message:</strong> ${error.message}<br>
                        <strong>Full Error:</strong> ${JSON.stringify(error, null, 2)}
                    </div>
                `;
                errorDiv.style.display = 'block';
                
                // Specific error handling
                if (error.code === 'auth/unauthorized-domain') {
                    document.getElementById('domain-status').innerHTML = '❌ <span class="status-bad">Domain not authorized in Firebase</span>';
                } else if (error.code === 'auth/popup-blocked') {
                    resultDiv.innerHTML += '<br><span class="status-warning">⚠️ Popup blocked - try allowing popups</span>';
                }
            }
        }

        // Test popup sign-in specifically
        async function testPopupSignIn() {
            const resultDiv = document.getElementById('oauth-result');
            
            resultDiv.innerHTML = '⏳ Testing popup-specific sign-in...';
            
            try {
                const provider = new firebase.auth.GoogleAuthProvider();
                const result = await auth.signInWithPopup(provider);
                
                resultDiv.innerHTML = `
                    <div class="status-good">
                        ✅ <strong>Popup Success!</strong><br>
                        User: ${result.user.email}
                    </div>
                `;
                
            } catch (error) {
                console.error('❌ Popup sign-in error:', error);
                resultDiv.innerHTML = `<div class="status-bad">❌ <strong>Popup Failed:</strong> ${error.code}</div>`;
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            updateEnvironmentInfo();
            testFirebaseStatus();
            
            // Check domain authorization
            const isProduction = window.location.hostname.includes('barberbrotherz.com');
            if (isProduction) {
                document.getElementById('domain-status').innerHTML = '🌐 <span class="status-warning">Production domain - checking authorization...</span>';
            } else {
                document.getElementById('domain-status').innerHTML = '🏠 <span class="status-good">Local domain - should be authorized</span>';
            }
        });
    </script>
</body>
</html>
