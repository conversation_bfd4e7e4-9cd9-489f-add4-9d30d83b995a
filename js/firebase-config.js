/**
 * Firebase Configuration - Browser Compatible Version
 * Centralized Firebase setup for Barber Brothers Legacy
 * Uses Firebase v10 CDN for better compatibility and features
 */

// Import Firebase v10 modules from CDN
import { initializeApp, getApps } from "https://www.gstatic.com/firebasejs/10.14.1/firebase-app.js";
import { getAuth } from "https://www.gstatic.com/firebasejs/10.14.1/firebase-auth.js";
import { getFirestore } from "https://www.gstatic.com/firebasejs/10.14.1/firebase-firestore.js";
import { getStorage } from "https://www.gstatic.com/firebasejs/10.14.1/firebase-storage.js";
import { getAnalytics, isSupported } from "https://www.gstatic.com/firebasejs/10.14.1/firebase-analytics.js";

// Firebase configuration - using your new Firebase project
const firebaseConfig = {
  apiKey: "AIzaSyBLFm5Kmdw4yA6fHqB1YFgzrJPmzE46CNM",
  authDomain: "barberbrothers-45d06.firebaseapp.com",
  projectId: "barberbrothers-45d06",
  storageBucket: "barberbrothers-45d06.firebasestorage.app",
  messagingSenderId: "1064307847156",
  appId: "1:1064307847156:web:5eed1300bd16839804d3a1",
  measurementId: "G-MHG8DQY38J"
};

// Initialize Firebase app (prevent multiple initialization)
let app;
if (!getApps().length) {
  app = initializeApp(firebaseConfig);
  console.log('✅ Firebase initialized successfully');
} else {
  app = getApps()[0];
  console.log('✅ Firebase app already initialized');
}

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

// Initialize Analytics (only if supported)
let analytics = null;
isSupported().then((supported) => {
  if (supported) {
    analytics = getAnalytics(app);
    console.log('✅ Firebase Analytics initialized');
  }
}).catch((error) => {
  console.warn('⚠️ Firebase Analytics not supported:', error);
});

export { analytics };

// Export the app instance
export { app };

// Global Firebase instance for legacy compatibility
window.firebaseApp = app;
window.firebaseAuth = auth;
window.firebaseDb = db;
window.firebaseStorage = storage;

// Configuration info for debugging
export const firebaseInfo = {
  projectId: firebaseConfig.projectId,
  authDomain: firebaseConfig.authDomain,
  version: "10.14.1",
  services: ["auth", "firestore", "storage", "analytics"],
  initialized: true,
  timestamp: new Date().toISOString()
};

console.log('🔥 Firebase configuration loaded:', firebaseInfo);
