// Simple Community Feed Implementation
// This creates a basic community feed without complex React imports

console.log('Community Feed: Initializing...');

// Simple community feed component
function createCommunityFeed() {
  return `
    <div class="community-feed-container">
      <div class="feed-header mb-4">
        <h3 class="text-white mb-3">
          <i class="fas fa-users me-2"></i>Community Feed
        </h3>
        <p class="text-muted">Connect with fellow barbers and clients</p>
      </div>

      <!-- Post Composer -->
      <div class="post-composer mb-4 p-3 bg-black bg-opacity-50 rounded" id="post-composer">
        <!-- Content will be dynamically updated based on auth state -->
      </div>

      <!-- Sample Posts -->
      <div class="posts-container">
        ${createSamplePosts()}
      </div>

      <!-- Load More -->
      <div class="text-center mt-4">
        <button class="btn btn-outline-danger">
          <i class="fas fa-plus me-2"></i>Load More Posts
        </button>
      </div>
    </div>
  `;
}

// Create HTML for a single post
function createPostHTML(post) {
  return `
    <div class="post-card mb-4 p-3 bg-black bg-opacity-50 rounded border border-danger border-opacity-25">
      <div class="post-header d-flex align-items-center mb-3">
        <img src="${post.avatar}" alt="${post.author}" class="rounded-circle me-3" width="40" height="40">
        <div>
          <h6 class="text-white mb-0">${post.author}</h6>
          <small class="text-muted">${post.time}</small>
        </div>
      </div>

      <div class="post-content mb-3">
        <p class="text-white">${post.content}</p>
        ${post.image ? `<img src="${post.image}" alt="Post image" class="img-fluid rounded mb-3" style="max-height: 300px; width: 100%; object-fit: cover;">` : ''}
      </div>

      <div class="post-stats d-flex align-items-center text-muted mb-3">
        <span class="like-count"><i class="fas fa-heart text-danger me-1"></i> ${post.likes}</span>
        <span class="ms-3"><i class="fas fa-comment me-1"></i> ${post.comments}</span>
        <span class="ms-3"><i class="fas fa-share me-1"></i> ${post.shares}</span>
      </div>

      <hr class="border-secondary">

      <div class="post-actions d-flex">
        <button class="btn btn-link text-white flex-grow-1 post-like-btn" data-post-id="${post.id}">
          <i class="far fa-heart me-1"></i> Like
        </button>
        <button class="btn btn-link text-white flex-grow-1">
          <i class="far fa-comment me-1"></i> Comment
        </button>
        <button class="btn btn-link text-white flex-grow-1">
          <i class="far fa-share-square me-1"></i> Share
        </button>
      </div>
    </div>
  `;
}

// Create sample posts for demonstration
function createSamplePosts() {
  const samplePosts = [
    {
      id: 1,
      author: "Andre The Barber",
      avatar: "images/time.jpeg",
      time: "2 hours ago",
      content: "Just finished this amazing fade! What do you think? #FreshCuts #BarberLife",
      image: "images/IMG_1073.jpeg",
      likes: 45,
      comments: 12,
      shares: 3
    },
    {
      id: 2,
      author: "Barber Brothers Media",
      avatar: "images/time.jpeg",
      time: "5 hours ago",
      content: "Tips for maintaining your fade between appointments: 1. Use quality hair products 2. Regular washing 3. Light trimming of edges",
      likes: 32,
      comments: 8,
      shares: 15
    },
    {
      id: 3,
      author: "Client Testimonial",
      avatar: "images/time.jpeg",
      time: "1 day ago",
      content: "Best barber experience I've ever had! Andre really knows his craft. Highly recommend! 💯",
      likes: 67,
      comments: 23,
      shares: 8
    }
  ];

  return samplePosts.map(post => createPostHTML(post)).join('');
}

// Check if user is authenticated
function isUserAuthenticated() {
  // Check for stored authentication data
  const authData = localStorage.getItem('authData');
  return authData && JSON.parse(authData).isAuthenticated;
}

// Get current user info
function getCurrentUser() {
  const authData = localStorage.getItem('authData');
  if (authData) {
    const data = JSON.parse(authData);
    return data.user || { name: 'User', avatar: 'images/time.jpeg' };
  }
  return { name: 'User', avatar: 'images/time.jpeg' };
}

// Create post composer based on auth state
function createPostComposer() {
  const isAuth = isUserAuthenticated();
  const user = getCurrentUser();

  if (isAuth) {
    return `
      <div class="d-flex align-items-center mb-3">
        <img src="${user.avatar || 'images/time.jpeg'}" alt="${user.name}" class="rounded-circle me-3" width="40" height="40">
        <input type="text" class="form-control bg-dark text-white border-danger"
               placeholder="Share your latest work or thoughts..." id="post-input">
      </div>
      <div class="d-flex justify-content-between align-items-center">
        <div class="post-options">
          <button class="btn btn-outline-light btn-sm me-2">
            <i class="fas fa-image me-1"></i>Photo
          </button>
          <button class="btn btn-outline-light btn-sm">
            <i class="fas fa-video me-1"></i>Video
          </button>
        </div>
        <button class="btn btn-danger" id="post-btn">
          <i class="fas fa-paper-plane me-1"></i>Post
        </button>
      </div>
    `;
  } else {
    return `
      <div class="text-center py-4">
        <i class="fas fa-lock fa-2x text-muted mb-3"></i>
        <h5 class="text-white mb-3">Sign in to participate</h5>
        <p class="text-muted mb-3">Join the Barber Brothers community to share posts, like content, and connect with others.</p>
                                <button class="btn btn-danger" onclick="showSignInModal()">
                            <i class="fas fa-user me-2"></i>Sign In
                        </button>
      </div>
    `;
  }
}

// Initialize the community feed
function initializeCommunityFeed() {
  console.log('Community Feed: Looking for mount point...');

  // Try to find the React mount point
  let mountPoint = document.getElementById('react-feed-mount');

  // Fallback to the main community root if the specific mount point doesn't exist yet
  if (!mountPoint) {
    mountPoint = document.getElementById('community-feed-root');
  }

  if (mountPoint) {
    console.log('Community Feed: Mount point found, rendering feed...');

    // Clear any existing content
    mountPoint.innerHTML = '';

    // Insert the community feed HTML
    mountPoint.innerHTML = createCommunityFeed();

    // Update post composer based on auth state
    updatePostComposer();

    // Add event listeners
    setupEventListeners();

    console.log('Community feed mounted successfully');
  } else {
    console.warn('Community feed mount point not found, retrying in 1 second...');
    setTimeout(initializeCommunityFeed, 1000);
  }
}

// Update post composer based on authentication state
function updatePostComposer() {
  const postComposer = document.getElementById('post-composer');
  if (postComposer) {
    postComposer.innerHTML = createPostComposer();
  }
}

// Setup event listeners for interactive elements
function setupEventListeners() {
  // Post button
  const postBtn = document.getElementById('post-btn');
  const postInput = document.getElementById('post-input');

  if (postBtn && postInput) {
    postBtn.addEventListener('click', function() {
      const content = postInput.value.trim();
      if (content) {
        createNewPost(content);
        postInput.value = '';
      }
    });

    // Allow Enter key to post
    postInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        const content = this.value.trim();
        if (content) {
          createNewPost(content);
          this.value = '';
        }
      }
    });
  }

  // Like buttons
  document.querySelectorAll('.post-like-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      const icon = this.querySelector('i');
      const isLiked = icon.classList.contains('fas');
      const postId = this.dataset.postId;

      if (isLiked) {
        icon.classList.remove('fas', 'text-danger');
        icon.classList.add('far');
        this.innerHTML = '<i class="far fa-heart me-1"></i> Like';
        updateLikeCount(postId, -1);
      } else {
        icon.classList.remove('far');
        icon.classList.add('fas', 'text-danger');
        this.innerHTML = '<i class="fas fa-heart text-danger me-1"></i> Liked';
        updateLikeCount(postId, 1);
        showToast('Post liked! ❤️');
      }
    });
  });

  // Comment buttons
  document.querySelectorAll('.post-actions .btn-link:nth-child(2)').forEach(btn => {
    btn.addEventListener('click', function() {
      const postCard = this.closest('.post-card');
      let commentSection = postCard.querySelector('.comment-section');

      if (!commentSection) {
        commentSection = document.createElement('div');
        commentSection.className = 'comment-section mt-3 p-3 bg-dark rounded';
        commentSection.innerHTML = `
          <div class="d-flex mb-3">
            <img src="images/time.jpeg" alt="User" class="rounded-circle me-2" width="30" height="30">
            <input type="text" class="form-control bg-black text-white border-danger comment-input"
                   placeholder="Write a comment...">
            <button class="btn btn-danger btn-sm ms-2 comment-submit">Post</button>
          </div>
          <div class="comments-list"></div>
        `;
        postCard.appendChild(commentSection);

        // Add comment functionality
        const commentInput = commentSection.querySelector('.comment-input');
        const commentSubmit = commentSection.querySelector('.comment-submit');

        commentSubmit.addEventListener('click', () => {
          const comment = commentInput.value.trim();
          if (comment) {
            addComment(commentSection, comment);
            commentInput.value = '';
          }
        });

        commentInput.addEventListener('keypress', (e) => {
          if (e.key === 'Enter') {
            const comment = commentInput.value.trim();
            if (comment) {
              addComment(commentSection, comment);
              commentInput.value = '';
            }
          }
        });
      } else {
        // Toggle comment section
        commentSection.style.display = commentSection.style.display === 'none' ? 'block' : 'none';
      }
    });
  });
}

// Create a new post
function createNewPost(content) {
  const postsContainer = document.querySelector('.posts-container');
  if (postsContainer) {
    const newPost = {
      id: Date.now(),
      author: "You",
      avatar: "images/time.jpeg",
      time: "Just now",
      content: content,
      likes: 0,
      comments: 0,
      shares: 0
    };

    const postHTML = createPostHTML(newPost);
    postsContainer.insertAdjacentHTML('afterbegin', postHTML);

    // Re-setup event listeners for the new post
    setupPostEventListeners(postsContainer.firstElementChild);

    showToast('Post created successfully! 🎉');
  }
}



// Setup event listeners for a specific post
function setupPostEventListeners(postElement) {
  const likeBtn = postElement.querySelector('.post-like-btn');
  if (likeBtn) {
    likeBtn.addEventListener('click', function() {
      const icon = this.querySelector('i');
      const isLiked = icon.classList.contains('fas');
      const postId = this.dataset.postId;

      if (isLiked) {
        icon.classList.remove('fas', 'text-danger');
        icon.classList.add('far');
        this.innerHTML = '<i class="far fa-heart me-1"></i> Like';
        updateLikeCount(postId, -1);
      } else {
        icon.classList.remove('far');
        icon.classList.add('fas', 'text-danger');
        this.innerHTML = '<i class="fas fa-heart text-danger me-1"></i> Liked';
        updateLikeCount(postId, 1);
        showToast('Post liked! ❤️');
      }
    });
  }
}

// Update like count
function updateLikeCount(postId, change) {
  const postCard = document.querySelector(`[data-post-id="${postId}"]`).closest('.post-card');
  const likeCountElement = postCard.querySelector('.like-count');
  if (likeCountElement) {
    const currentCount = parseInt(likeCountElement.textContent.match(/\d+/)[0]);
    const newCount = Math.max(0, currentCount + change);
    likeCountElement.innerHTML = `<i class="fas fa-heart text-danger me-1"></i> ${newCount}`;
  }
}

// Add a comment
function addComment(commentSection, commentText) {
  const commentsList = commentSection.querySelector('.comments-list');
  const commentHTML = `
    <div class="comment mb-2 p-2 bg-black rounded">
      <div class="d-flex align-items-start">
        <img src="images/time.jpeg" alt="User" class="rounded-circle me-2" width="25" height="25">
        <div>
          <small class="text-white fw-bold">You</small>
          <small class="text-muted ms-2">now</small>
          <p class="text-white mb-0 mt-1">${commentText}</p>
        </div>
      </div>
    </div>
  `;
  commentsList.insertAdjacentHTML('beforeend', commentHTML);
  showToast('Comment added! 💬');
}

// Show toast notification
function showToast(message) {
  // Create toast if it doesn't exist
  let toast = document.getElementById('community-toast');
  if (!toast) {
    toast = document.createElement('div');
    toast.id = 'community-toast';
    toast.className = 'toast position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="toast-header bg-danger text-white">
        <strong class="me-auto">Community</strong>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
      </div>
      <div class="toast-body bg-dark text-white"></div>
    `;
    document.body.appendChild(toast);
  }

  // Update message and show
  toast.querySelector('.toast-body').textContent = message;
  const bsToast = new bootstrap.Toast(toast);
  bsToast.show();
}

// Listen for authentication state changes
window.addEventListener('authStateChanged', function(event) {
  console.log('Community Feed: Auth state changed', event.detail);
  updatePostComposer();
});

// Initialize when DOM is ready or community hub is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeCommunityFeed);
} else {
  // Check if community hub is ready
  if (window.CommunityHub) {
    initializeCommunityFeed();
  } else {
    // Wait for community hub to be ready
    window.addEventListener('communityHubReady', initializeCommunityFeed);
    // Also try after a short delay as fallback
    setTimeout(initializeCommunityFeed, 2000);
  }
}
