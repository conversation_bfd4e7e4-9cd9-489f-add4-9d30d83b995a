document.addEventListener('DOMContentLoaded', function() {
    console.log('Service calculator loaded');

    // Initialize service price calculators
    initServiceCalculators();
});





// Initialize service calculators and UI
function initServiceCalculators() {
    // Initialize service price calculators
    if (typeof initAdultServiceCalculator === 'function') {
        initAdultServiceCalculator();
    } else {
        console.log('⚠️ initAdultServiceCalculator not defined');
    }

    if (typeof initKidsServiceCalculator === 'function') {
        initKidsServiceCalculator();
    } else {
        console.log('⚠️ initKidsServiceCalculator not defined');
    }

    if (typeof initFullServiceCalculator === 'function') {
        initFullServiceCalculator();
    } else {
        console.log('⚠️ initFullServiceCalculator not defined');
    }

    // Initialize sign-out functionality
    const signOutLink = document.getElementById('sign-out-link');
    if (signOutLink) {
        signOutLink.addEventListener('click', function(e) {
            e.preventDefault();
            signOut();
        });
    }

    console.log('Service calculators and UI initialized');
}

// Sign-in function (placeholder for future implementation)
function signInWithGoogle(serviceType) {
    showMessage('Sign-in functionality is currently being updated. Please check back soon!', 'info');
}

// Sign out function
function signOut() {
    console.log('User signed out successfully');
    updateUIForSignedOutUser();
    showMessage('You have been signed out successfully.', 'success');
}

// Helper function to get service name
function getServiceName(serviceType) {
    switch (serviceType) {
        case 'adult':
            return 'Adult Haircut';
        case 'kids':
            return 'Kids Haircut';
        case 'full':
            return 'Full Service';
        case 'navbar':
            return 'service';
        default:
            return 'service';
    }
}

// Function to show messages (success/error)
function showMessage(message, type = 'success') {
    // Remove any existing messages
    const existingMessages = document.querySelectorAll('.auth-alert');
    existingMessages.forEach(msg => msg.remove());

    // Create new message
    const messageDiv = document.createElement('div');
    messageDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} auth-alert position-fixed`;
    messageDiv.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;

    messageDiv.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            <div>
                <strong>${type === 'success' ? 'Success!' : 'Error'}</strong>
                <div>${message}</div>
            </div>
            <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    // Add to body
    document.body.appendChild(messageDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 5000);
}

import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/10.14.1/firebase-auth.js";

// Monitor authentication state
function monitorAuthState() {
    if (typeof firebase !== 'undefined') {
        const auth = getAuth();

        onAuthStateChanged(auth, (user) => {
            if (user) {
                console.log('User is signed in:', user.displayName);
                updateUIForSignedInUser(user);
            } else {
                console.log('User is signed out');
                updateUIForSignedOutUser();
            }
        });
    }
}

// Update UI for signed in user
function updateUIForSignedInUser(user) {
    // Update navbar to show user menu
    const userMenu = document.getElementById('user-menu');
    const signinMenu = document.getElementById('signin-menu');
    const userAvatar = document.getElementById('user-avatar');
    const userName = document.getElementById('user-name');

    if (userMenu && signinMenu) {
        userMenu.classList.remove('d-none');
        signinMenu.classList.add('d-none');

        if (userAvatar && user.photoURL) {
            userAvatar.src = user.photoURL;
        }
        if (userName) {
            userName.textContent = user.displayName || 'User';
        }
    }

    // Update service card overlays to show "Book Now" instead of "Sign in to book"
    const serviceOverlays = document.querySelectorAll('.service-overlay span');
    serviceOverlays.forEach(overlay => {
        overlay.textContent = 'Click to book';
    });

    // Update service cards to go directly to booking instead of sign-in
    const serviceCards = document.querySelectorAll('.google-signin-btn');
    serviceCards.forEach(card => {
        card.classList.remove('google-signin-btn');
        card.classList.add('booking-btn');

        // Remove old event listeners by cloning the element
        const newCard = card.cloneNode(true);
        card.parentNode.replaceChild(newCard, card);

        // Add new event listener for booking
        newCard.addEventListener('click', function() {
            const serviceType = this.dataset.service;
            const serviceName = getServiceName(serviceType);

            // Pre-select service in contact form
            const serviceSelect = document.getElementById('service');
            if (serviceSelect) {
                const option = Array.from(serviceSelect.options).find(opt =>
                    opt.textContent.toLowerCase().includes(serviceName.toLowerCase())
                );
                if (option) {
                    option.selected = true;
                }
            }

            // Scroll to contact section
            const contactSection = document.getElementById('contact');
            if (contactSection) {
                contactSection.scrollIntoView({ behavior: 'smooth' });
            }

            showMessage(`Ready to book your ${serviceName}! Please fill out the form below.`, 'success');
        });
    });
}

// Update UI for signed out user
function updateUIForSignedOutUser() {
    // Update navbar to show sign-in button
    const userMenu = document.getElementById('user-menu');
    const signinMenu = document.getElementById('signin-menu');

    if (userMenu && signinMenu) {
        userMenu.classList.add('d-none');
        signinMenu.classList.remove('d-none');
    }

    // Update service card overlays back to "Sign in to book"
    const serviceOverlays = document.querySelectorAll('.service-overlay span');
    serviceOverlays.forEach(overlay => {
        overlay.textContent = 'Sign in to book';
    });

    // Update service cards back to sign-in functionality
    const serviceCards = document.querySelectorAll('.booking-btn');
    serviceCards.forEach(card => {
        card.classList.remove('booking-btn');

        // Remove old event listeners by cloning the element
        const newCard = card.cloneNode(true);
        card.parentNode.replaceChild(newCard, card);

        // Add sign-in event listener using the redirect approach
        newCard.addEventListener('click', function() {
            const serviceType = this.dataset.service;
            if (typeof window.redirectToGoogleSignIn === 'function') {
                window.redirectToGoogleSignIn(serviceType);
            } else {
                console.error('redirectToGoogleSignIn function not available');
            }
        });
    });
}

// Initialize auth state monitoring when Firebase is ready
setTimeout(() => {
    monitorAuthState();
}, 1000);