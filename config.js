/**
 * Firebase Configuration - Browser Compatible Version
 * Uses Firebase v8 compat mode for better compatibility with existing code
 */

// Firebase configuration for your new project
const firebaseConfig = {
  apiKey: "AIzaSyBLFm5Kmdw4yA6fHqB1YFgzrJPmzE46CNM",
  authDomain: "barberbrothers-45d06.firebaseapp.com",
  projectId: "barberbrothers-45d06",
  storageBucket: "barberbrothers-45d06.firebasestorage.app",
  messagingSenderId: "1064307847156",
  appId: "1:1064307847156:web:5eed1300bd16839804d3a1",
  measurementId: "G-MHG8DQY38J"
};

// Initialize Firebase (only if not already initialized)
if (!firebase.apps.length) {
  firebase.initializeApp(firebaseConfig);
  console.log('✅ Firebase initialized with new configuration');
} else {
  console.log('✅ Firebase already initialized');
}

// Make sure auth is available globally
window.firebaseAuth = firebase.auth();
window.firebaseDb = firebase.firestore();

console.log('🔥 Firebase config loaded successfully');
