/**
 * Google OAuth Configuration for Barber Brothers Legacy
 * This file contains the Google OAuth client configuration
 * Safe to expose client ID (never expose client secret in frontend)
 */

// Google OAuth Configuration
const GOOGLE_AUTH_CONFIG = {
    clientId: '902371497891-ub0nfb5iu5ke57pi13vqilrdp74ktt0v.apps.googleusercontent.com',
    redirectUri: `${window.location.origin}/auth-callback.html`,
    scopes: [
        'openid',
        'email',
        'profile'
    ],
    responseType: 'code',
    accessType: 'offline',
    prompt: 'consent'
};

// Generate Google OAuth URL
function getGoogleAuthUrl() {
    const params = new URLSearchParams({
        client_id: GOOGLE_AUTH_CONFIG.clientId,
        redirect_uri: GOOGLE_AUTH_CONFIG.redirectUri,
        scope: GOOGLE_AUTH_CONFIG.scopes.join(' '),
        response_type: GOOGLE_AUTH_CONFIG.responseType,
        access_type: GOOGLE_AUTH_CONFIG.accessType,
        prompt: GOOGLE_AUTH_CONFIG.prompt
    });

    return `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;
}

// Initialize Google Sign-In for Firebase
function initGoogleSignIn() {
    // Configure Firebase Auth to use the correct client ID
    if (typeof firebase !== 'undefined' && firebase.auth) {
        const provider = new firebase.auth.GoogleAuthProvider();
        provider.addScope('email');
        provider.addScope('profile');
        
        // Set custom parameters
        provider.setCustomParameters({
            'prompt': 'select_account'
        });
        
        console.log('✅ Google Sign-In initialized with client ID:', GOOGLE_AUTH_CONFIG.clientId);
        return provider;
    } else {
        console.error('❌ Firebase not loaded or auth not available');
        return null;
    }
}

// Enhanced Google Sign-In function with better error handling
function signInWithGoogleEnhanced() {
    const provider = initGoogleSignIn();
    if (!provider) {
        console.error('❌ Google provider not initialized');
        return Promise.reject(new Error('Google provider not initialized'));
    }

    return firebase.auth().signInWithPopup(provider)
        .then((result) => {
            console.log('✅ Google sign-in successful:', result.user.email);
            return result;
        })
        .catch((error) => {
            console.error('❌ Google sign-in error:', error);
            
            // Handle specific error cases
            if (error.code === 'auth/popup-closed-by-user') {
                console.log('ℹ️ User closed the popup');
            } else if (error.code === 'auth/popup-blocked') {
                console.log('⚠️ Popup was blocked by browser');
                // Fallback to redirect method
                return firebase.auth().signInWithRedirect(provider);
            } else if (error.code === 'auth/unauthorized-domain') {
                console.error('❌ Domain not authorized. Check Firebase console.');
            }
            
            throw error;
        });
}

// Debug function to check configuration
function debugGoogleAuthConfig() {
    console.log('🔧 Google Auth Configuration Debug:');
    console.log('Client ID:', GOOGLE_AUTH_CONFIG.clientId);
    console.log('Redirect URI:', GOOGLE_AUTH_CONFIG.redirectUri);
    console.log('Scopes:', GOOGLE_AUTH_CONFIG.scopes);
    console.log('Current Origin:', window.location.origin);
    console.log('Generated Auth URL:', getGoogleAuthUrl());
}

// Export for global access
window.GOOGLE_AUTH_CONFIG = GOOGLE_AUTH_CONFIG;
window.getGoogleAuthUrl = getGoogleAuthUrl;
window.initGoogleSignIn = initGoogleSignIn;
window.signInWithGoogleEnhanced = signInWithGoogleEnhanced;
window.debugGoogleAuthConfig = debugGoogleAuthConfig;

console.log('✅ Google Auth Config loaded successfully');
