<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Firebase Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #121212;
            color: #fff;
        }
        .test-result {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        button {
            background: #D90000;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #b30000;
        }
        pre {
            background: #000;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔥 Simple Firebase Authentication Test</h1>
    
    <div class="test-result">
        <h3>📍 Environment Check</h3>
        <p><strong>Domain:</strong> <span id="domain"></span></p>
        <p><strong>Protocol:</strong> <span id="protocol"></span></p>
        <p><strong>Full URL:</strong> <span id="full-url"></span></p>
    </div>

    <div class="test-result">
        <h3>🔧 Firebase Status</h3>
        <p id="firebase-status">⏳ Checking...</p>
        <p id="auth-status">⏳ Checking...</p>
    </div>

    <div class="test-result">
        <h3>🧪 Authentication Test</h3>
        <button onclick="testBasicAuth()">Test Firebase Auth</button>
        <button onclick="testGoogleProvider()">Test Google Provider</button>
        <div id="test-results"></div>
    </div>

    <div class="test-result">
        <h3>📋 Debug Information</h3>
        <pre id="debug-info"></pre>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/10.14.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.14.1/firebase-auth-compat.js"></script>
    
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBLFm5Kmdw4yA6fHqB1YFgzrJPmzE46CNM",
            authDomain: "barberbrothers-45d06.firebaseapp.com",
            projectId: "barberbrothers-45d06",
            storageBucket: "barberbrothers-45d06.firebasestorage.app",
            messagingSenderId: "1064307847156",
            appId: "1:1064307847156:web:5eed1300bd16839804d3a1",
            measurementId: "G-MHG8DQY38J"
        };

        let auth;
        let debugInfo = [];

        function addDebug(message) {
            debugInfo.push(`${new Date().toISOString()}: ${message}`);
            document.getElementById('debug-info').textContent = debugInfo.join('\n');
            console.log(message);
        }

        // Initialize Firebase
        try {
            firebase.initializeApp(firebaseConfig);
            auth = firebase.auth();
            addDebug('✅ Firebase initialized successfully');
            document.getElementById('firebase-status').innerHTML = '<span class="success">✅ Firebase loaded</span>';
            document.getElementById('auth-status').innerHTML = '<span class="success">✅ Auth service ready</span>';
        } catch (error) {
            addDebug('❌ Firebase initialization failed: ' + error.message);
            document.getElementById('firebase-status').innerHTML = '<span class="error">❌ Firebase failed</span>';
            document.getElementById('auth-status').innerHTML = '<span class="error">❌ Auth not available</span>';
        }

        // Update environment info
        function updateEnvironmentInfo() {
            document.getElementById('domain').textContent = window.location.hostname;
            document.getElementById('protocol').textContent = window.location.protocol;
            document.getElementById('full-url').textContent = window.location.href;
            
            addDebug(`Environment: ${window.location.hostname} (${window.location.protocol})`);
        }

        // Test basic Firebase Auth
        function testBasicAuth() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '⏳ Testing Firebase Auth...';
            
            try {
                if (!auth) {
                    throw new Error('Firebase Auth not initialized');
                }
                
                addDebug('Testing auth state listener...');
                
                // Test auth state listener
                const unsubscribe = auth.onAuthStateChanged((user) => {
                    if (user) {
                        addDebug(`✅ User signed in: ${user.email}`);
                        resultsDiv.innerHTML = `<span class="success">✅ Auth working - User: ${user.email}</span>`;
                    } else {
                        addDebug('ℹ️ No user signed in');
                        resultsDiv.innerHTML = '<span class="warning">⚠️ Auth working - No user signed in</span>';
                    }
                    unsubscribe(); // Remove listener after first check
                });
                
            } catch (error) {
                addDebug('❌ Basic auth test failed: ' + error.message);
                resultsDiv.innerHTML = `<span class="error">❌ Auth test failed: ${error.message}</span>`;
            }
        }

        // Test Google Provider
        function testGoogleProvider() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '⏳ Testing Google Provider...';
            
            try {
                addDebug('Creating Google Auth Provider...');
                const provider = new firebase.auth.GoogleAuthProvider();
                provider.addScope('email');
                provider.addScope('profile');
                
                addDebug('✅ Google provider created successfully');
                resultsDiv.innerHTML = '<span class="success">✅ Google provider ready</span>';
                
                // Try to sign in
                addDebug('Attempting Google sign-in...');
                auth.signInWithPopup(provider)
                    .then((result) => {
                        addDebug(`✅ Google sign-in successful: ${result.user.email}`);
                        resultsDiv.innerHTML = `<span class="success">✅ Google sign-in successful: ${result.user.email}</span>`;
                    })
                    .catch((error) => {
                        addDebug(`❌ Google sign-in failed: ${error.code} - ${error.message}`);
                        resultsDiv.innerHTML = `<span class="error">❌ Google sign-in failed: ${error.code}</span>`;
                        
                        // Specific error handling
                        if (error.code === 'auth/unauthorized-domain') {
                            resultsDiv.innerHTML += '<br><span class="error">Domain not authorized in Firebase</span>';
                        } else if (error.code === 'auth/popup-blocked') {
                            resultsDiv.innerHTML += '<br><span class="warning">Popup blocked by browser</span>';
                        } else if (error.code === 'auth/operation-not-allowed') {
                            resultsDiv.innerHTML += '<br><span class="error">Google sign-in not enabled in Firebase</span>';
                        }
                    });
                
            } catch (error) {
                addDebug('❌ Google provider test failed: ' + error.message);
                resultsDiv.innerHTML = `<span class="error">❌ Provider test failed: ${error.message}</span>`;
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            updateEnvironmentInfo();
            addDebug('Page loaded, Firebase test ready');
        });
    </script>
</body>
</html>
