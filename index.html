<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Barber Brothers Legacy - Premium Cuts by <PERSON></title>

    <!-- Performance optimizations -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">

    <!-- Stylesheets with performance hints -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Oswald:wght@400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Firebase v8 compat CDN -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>

    <script src="config.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="#top">
                <img src="images/time.jpeg" alt="Barber Brothers Legacy Logo - Vintage Clock with Scissors" class="navbar-logo me-2">
                BARBER BROTHERS LEGACY
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <!-- Google Identity Services API (Security Updated) -->
            <script src="https://accounts.google.com/gsi/client" async defer></script>
            <!-- Security: Client ID is safe to expose, but never expose client secret -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#top">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#services">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#my-work">My Work</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#live-streams">Live</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#testimonials">Testimonials</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#community">
                            <i class="fas fa-users me-1"></i>Community
                        </a>
                    </li>
                    <!-- User Menu (hidden by default, shown when signed in) -->
                    <li class="nav-item dropdown d-none" id="user-menu">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <img id="user-avatar" src="" alt="User" class="rounded-circle me-2" width="30" height="30">
                            <span id="user-name">User</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="#contact"><i class="fas fa-calendar me-2"></i>Book Appointment</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="sign-out-link"><i class="fas fa-sign-out-alt me-2"></i>Sign Out</a></li>
                        </ul>
                    </li>
                    <!-- Sign In Button (shown when not signed in) -->
                    <li class="nav-item" id="signin-menu">
                        <button class="btn btn-outline-danger" id="google-signin-btn">
                            <i class="fab fa-google me-2"></i>Sign in with Google
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="top" class="hero">
        <div class="container">
            <h1>BARBER BROTHERS LEGACY</h1>
            <h2>PREMIUM CUTS BY ANDRE THE BARBER</h2>
            <p>Where Style Meets Precision</p>
            <a href="#contact" class="btn btn-danger scroll-to-booking">BOOK NOW</a>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-5">
        <div class="container">
            <h2 class="section-title">THE EXPERIENCE</h2>
            <div class="row align-items-center">
                <div class="col-md-6 mb-4 mb-md-0">
                    <div class="about-img-container">
                        <div class="about-img"></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h3>TRUST YOUR STRUGGLE</h3>
                    <p class="lead">Step into a world where style meets precision at Barber Brothers Legacy. Founded by Andre the Barber, a master barber from New Orleans, now shaping styles in Douglasville, Georgia, this shop is built on skill, passion, and dedication.</p>
                    <p>Every cut is more than just a service—it's a craft. With over 20 years of experience, Andre the Barber ensures every client leaves looking sharp, feeling fresh, and ready to take on the world.</p>
                    <p>This isn't just a barbershop. It's a legacy in the making.</p>
                    <p>Welcome to Barber Brothers Legacy. 💈</p>
                    <div class="mt-4">
                        <a href="#services" class="btn btn-danger btn-lg">View Our Services</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Haircut Gallery Section -->
    <section id="gallery" class="py-5">
        <div class="container">
            <h2 class="section-title text-center mb-5">HAIRCUT GALLERY</h2>
            <div class="row justify-content-center gallery-mobile-centered">
                <div class="col-lg-4 col-md-6 col-sm-6 col-12 mb-4">
                    <div class="gallery-item">
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='500'%3E%3Crect width='100%25' height='100%25' fill='%23f8f9fa'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%23666'%3ELoading...%3C/text%3E%3C/svg%3E"
                             data-src="images/IMG_1073.jpeg"
                             alt="Professional Haircut 1 - Andre The Barber"
                             class="rounded shadow gallery-lazy-image"
                             loading="lazy">
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 col-sm-6 col-12 mb-4">
                    <div class="gallery-item">
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='500'%3E%3Crect width='100%25' height='100%25' fill='%23f8f9fa'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%23666'%3ELoading...%3C/text%3E%3C/svg%3E"
                             data-src="images/IMG_0920.jpeg"
                             alt="Professional Haircut 2 - Andre The Barber"
                             class="rounded shadow gallery-lazy-image"
                             loading="lazy">
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 col-sm-6 col-12 mb-4">
                    <div class="gallery-item">
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='500'%3E%3Crect width='100%25' height='100%25' fill='%23f8f9fa'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%23666'%3ELoading...%3C/text%3E%3C/svg%3E"
                             data-src="images/IMG_0980.jpeg"
                             alt="Professional Haircut 3 - Andre The Barber"
                             class="rounded shadow gallery-lazy-image"
                             loading="lazy">
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 col-sm-6 col-12 mb-4">
                    <div class="gallery-item">
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='500'%3E%3Crect width='100%25' height='100%25' fill='%23f8f9fa'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%23666'%3ELoading...%3C/text%3E%3C/svg%3E"
                             data-src="images/IMG_0994.jpeg"
                             alt="Professional Haircut 4 - Andre The Barber"
                             class="rounded shadow gallery-lazy-image"
                             loading="lazy">
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 col-sm-6 col-12 mb-4">
                    <div class="gallery-item">
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='500'%3E%3Crect width='100%25' height='100%25' fill='%23f8f9fa'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%23666'%3ELoading...%3C/text%3E%3C/svg%3E"
                             data-src="images/d1.jpg"
                             alt="Professional Haircut 5 - Andre The Barber"
                             class="rounded shadow gallery-lazy-image"
                             loading="lazy">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- My Work Carousel Section -->
    <section id="my-work" class="py-5 bg-dark">
        <div class="container">
            <h2 class="section-title text-center mb-5 text-white">MY WORK</h2>
            <div class="row justify-content-center">
                <div class="col-lg-8 col-md-10">
                    <div class="work-carousel-container">
                        <div class="work-carousel-card">
                            <div class="carousel-image-container">
                                <img id="carousel-image" src="images/a1.JPG" alt="Andre's Work" class="carousel-image">
                                <div class="carousel-overlay">
                                    <div class="carousel-counter">
                                        <span id="current-image">1</span> / <span id="total-images">21</span>
                                    </div>
                                </div>
                            </div>
                            <div class="carousel-caption">
                                <h5>Professional Cuts by Andre The Barber</h5>
                                <p>Showcasing precision, style, and artistry in every cut</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-5 bg-light">
        <div class="container">
            <h2 class="section-title text-center mb-5">OUR SERVICES</h2>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="service-card" role="button" tabindex="0" data-modal-id="adultHaircutModal">
                        <div class="service-icon">
                            <i class="fas fa-cut"></i>
                        </div>
                        <h3>Adult Haircuts</h3>
                        <p>Premium haircuts tailored to your style.</p>
                        <p class="price">$40 - $50</p>
                        <div class="service-overlay">
                            <span>Sign in to book</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="service-card" role="button" tabindex="0" data-modal-id="kidsHaircutModal">
                        <div class="service-icon">
                            <i class="fas fa-child"></i>
                        </div>
                        <h3>Kids Haircuts</h3>
                        <p>Stylish cuts for the little ones.</p>
                        <p class="price">$30 - $35</p>
                        <div class="service-overlay">
                            <span>Sign in to book</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="service-card" role="button" tabindex="0" data-modal-id="fullServiceModal">
                        <div class="service-icon">
                            <i class="fas fa-spa"></i>
                        </div>
                        <h3>Full Service</h3>
                        <p>Complete grooming experience for adults.</p>
                        <p class="price">$60</p>
                        <div class="service-overlay">
                            <span>Sign in to book</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Service Modals -->
    <!-- Adult Haircut Modal -->
    <div class="modal fade" id="adultHaircutModal" tabindex="-1" aria-labelledby="adultHaircutModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="adultHaircutModalLabel">ADULT HAIRCUTS SERVICE DETAILS</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="service-details">
                        <div class="service-selection-card">
                            <div class="service-options">
                                <!-- Base Service -->
                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="baseService" checked disabled>
                                        <label class="form-check-label" for="baseService">
                                            <span class="service-name">Standard Haircut</span>
                                            <span class="service-description">Professional haircut with precision detail</span>
                                            <span class="price-tag">$40</span>
                                        </label>
                                    </div>
                                </div>

                                <!-- Additional Services -->
                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input service-addon" id="hairWash" data-price="5">
                                        <label class="form-check-label" for="hairWash">
                                            <span class="service-name">Hair Wash & Scalp Refresh</span>
                                            <span class="service-description">Relaxing shampoo and conditioning treatment</span>
                                            <span class="price-tag">+$5</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input service-addon" id="beardTrim" data-price="5">
                                        <label class="form-check-label" for="beardTrim">
                                            <span class="service-name">Beard Trim & Line-Up</span>
                                            <span class="service-description">Clean and precise beard shaping</span>
                                            <span class="price-tag">+$5</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input service-addon" id="razorFinish" data-price="5">
                                        <label class="form-check-label" for="razorFinish">
                                            <span class="service-name">Razor Finish</span>
                                            <span class="service-description">Sharp lines and clean edges</span>
                                            <span class="price-tag">+$5</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input service-addon" id="beardColor" data-price="10">
                                        <label class="form-check-label" for="beardColor">
                                            <span class="service-name">Beard Coloring</span>
                                            <span class="service-description">Professional beard color treatment</span>
                                            <span class="price-tag">+$10</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="total-price mt-4">
                                <h6>Total Price: <span id="totalPrice">$40</span></h6>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="#contact" class="btn btn-danger book-now-btn" data-service-type="adult" data-bs-dismiss="modal">Book Now</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Kids Haircut Modal -->
    <div class="modal fade" id="kidsHaircutModal" tabindex="-1" aria-labelledby="kidsHaircutModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="kidsHaircutModalLabel">KIDS HAIRCUTS SERVICE DETAILS</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="service-details">
                        <div class="service-selection-card">
                            <div class="service-options">
                                <!-- Base Service -->
                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="kidsBaseService" checked disabled>
                                        <label class="form-check-label" for="kidsBaseService">
                                            <span class="service-name">Basic Kids Cut</span>
                                            <span class="service-description">Basic haircut with light styling</span>
                                            <span class="price-tag">$30</span>
                                        </label>
                                    </div>
                                    <ul class="included-services mt-2">
                                        <li><i class="fas fa-check text-success me-2"></i>Child-friendly consultation</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Basic haircut</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Light styling</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Lollipop included!</li>
                                    </ul>
                                </div>

                                <!-- Additional Services -->
                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input kids-service-addon" id="kidsHairWash" data-price="5">
                                        <label class="form-check-label" for="kidsHairWash">
                                            <span class="service-name">Hair Wash & Conditioning</span>
                                            <span class="service-description">Gentle cleansing and conditioning treatment</span>
                                            <span class="price-tag">+$5</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input kids-service-addon" id="kidsDesign" data-price="5">
                                        <label class="form-check-label" for="kidsDesign">
                                            <span class="service-name">Special Design/Pattern</span>
                                            <span class="service-description">Custom hair design or pattern (stars, lines, etc.)</span>
                                            <span class="price-tag">+$5</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input kids-service-addon" id="kidsColor" data-price="15">
                                        <label class="form-check-label" for="kidsColor">
                                            <span class="service-name">Hair Coloring</span>
                                            <span class="service-description">Professional hair color treatment</span>
                                            <span class="price-tag">+$15</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="total-price mt-4">
                                <h6>Total Price: <span id="kidsTotalPrice" style="color: #dc3545; font-weight: bold; font-size: 1.3em;">$30</span></h6>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="#contact" class="btn btn-danger book-now-btn" data-service-type="kids" data-bs-dismiss="modal">Book Now</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Full Service Modal -->
    <div class="modal fade" id="fullServiceModal" tabindex="-1" aria-labelledby="fullServiceModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="fullServiceModalLabel">Full Service Experience Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="service-details">
                        <div class="service-selection-card">
                            <div class="service-options">
                                <!-- Base Service -->
                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="fullServiceBase" checked disabled>
                                        <label class="form-check-label" for="fullServiceBase">
                                            <span class="service-name">Premium Full Service Package</span>
                                            <span class="service-description">Complete grooming experience</span>
                                            <span class="price-tag">$60</span>
                                        </label>
                                    </div>
                                    <ul class="included-services mt-2">
                                        <li><i class="fas fa-check text-success me-2"></i>Professional consultation</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Premium haircut</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Luxury shampoo & conditioning</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Scalp massage</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Hot towel service</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Beard trim & shaping</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Facial hair grooming</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Advanced styling</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Neck shave</li>
                                    </ul>
                                </div>

                                <!-- Additional Premium Services -->
                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input service-addon" id="fullPremiumColor" data-price="20">
                                        <label class="form-check-label" for="fullPremiumColor">
                                            <span class="service-name">Premium Color Treatment</span>
                                            <span class="service-description">Professional hair/beard coloring with premium products</span>
                                            <span class="price-tag">+$20</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input service-addon" id="fullFacialTreatment" data-price="15">
                                        <label class="form-check-label" for="fullFacialTreatment">
                                            <span class="service-name">Luxury Facial Treatment</span>
                                            <span class="service-description">Deep cleansing facial with premium skincare products</span>
                                            <span class="price-tag">+$15</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input service-addon" id="fullScalpTreatment" data-price="15">
                                        <label class="form-check-label" for="fullScalpTreatment">
                                            <span class="service-name">Advanced Scalp Treatment</span>
                                            <span class="service-description">Deep conditioning and scalp therapy</span>
                                            <span class="price-tag">+$15</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input service-addon" id="fullSteamTherapy" data-price="10">
                                        <label class="form-check-label" for="fullSteamTherapy">
                                            <span class="service-name">Steam Therapy Session</span>
                                            <span class="service-description">Relaxing steam treatment for face and scalp</span>
                                            <span class="price-tag">+$10</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="total-price mt-4">
                                <h6>Total Price: <span id="fullServiceTotalPrice">$60</span></h6>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="#contact" class="btn btn-danger book-now-btn" data-service-type="full" data-bs-dismiss="modal">Book Now</a>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Barber Brothers Media Styles */
        .profile-pic {
            border-radius: 50%;
            object-fit: cover;
        }
        
        .user-avatar {
            border-radius: 50%;
            object-fit: cover;
        }
        
        .user-avatar-sm {
            border-radius: 50%;
            object-fit: cover;
        }
        
        .social-profile-header {
            transition: transform 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .social-stats span {
            font-size: 0.85rem;
        }
        
        .social-post {
            transition: transform 0.2s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        
        .social-post:hover {
            transform: translateY(-3px);
        }
        
        .post-image {
            width: 100%;
            max-height: 400px;
            object-fit: cover;
        }
        
        .post-stats {
            font-size: 0.85rem;
        }

        .post-actions {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 0.5rem;
        }

        .post-actions .btn {
            border: none;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .post-actions .btn:hover {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545 !important;
            transform: translateY(-1px);
        }

        .comment-content {
            border-radius: 12px;
        }
        
        .form-control-dark {
            background-color: #2a2a2a;
            border-color: #444;
            color: #fff;
        }
        
        .form-control-dark:focus {
            background-color: #333;
            color: #fff;
            border-color: #dc3545;
            box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
        }

        /* Social Media Responsive Styles */
        @media (max-width: 768px) {
            .social-stats span {
                font-size: 0.8rem;
                margin: 0 0.5rem;
            }

            .post-actions .btn {
                padding: 0.4rem 0.8rem;
                font-size: 0.85rem;
            }

            .post-image {
                max-height: 300px;
            }

            .social-profile-header {
                padding: 1rem !important;
            }
        }

        @media (max-width: 576px) {
            .social-stats {
                flex-wrap: wrap;
                gap: 0.5rem;
            }

            .post-actions .btn {
                padding: 0.3rem 0.6rem;
                font-size: 0.8rem;
            }

            .post-actions .btn i {
                margin-right: 0.25rem !important;
            }

            .post-image {
                max-height: 250px;
            }
        }
        
        .service-card {
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .service-overlay {
            position: absolute;
            bottom: -40px;
            left: 0;
            right: 0;
            background: rgba(220, 53, 69, 0.9);
            color: white;
            padding: 10px;
            text-align: center;
            transition: bottom 0.3s ease;
        }

        .service-card:hover .service-overlay {
            bottom: 0;
        }

        .modal-content {
            border: none;
            border-radius: 15px;
        }

        .modal-header {
            background-color: #dc3545;
            color: white;
            border-radius: 15px 15px 0 0;
        }

        .modal-body {
            padding: 2rem;
        }

        .service-details li {
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .btn-close {
            background-color: white;
        }

        .service-selection-card {
            background: #fff;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .service-options {
            margin-top: 15px;
        }

        .form-check {
            padding: 10px 15px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .form-check:hover {
            background-color: rgba(220, 53, 69, 0.05);
        }

        .price-tag {
            float: right;
            color: #dc3545;
            font-weight: bold;
        }

        .total-price {
            border-top: 2px solid #eee;
            padding-top: 15px;
            text-align: right;
            font-size: 1.2em;
        }

        #totalPrice {
            color: #dc3545;
            font-weight: bold;
            font-size: 1.3em;
        }

        .service-option {
            border: 1px solid #eee;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .service-option:hover {
            background-color: rgba(220, 53, 69, 0.05);
            border-color: #dc3545;
        }

        .form-check {
            padding: 15px;
            margin: 0;
        }

        .form-check-label {
            display: flex;
            flex-direction: column;
            width: 100%;
            cursor: pointer;
        }

        .service-name {
            font-weight: bold;
            font-size: 1.1em;
            color: #333;
            margin-bottom: 4px;
        }

        .service-description {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 4px;
        }

        .price-tag {
            color: #dc3545;
            font-weight: bold;
            font-size: 1.1em;
            align-self: flex-end;
            margin-top: -35px;
        }

        .modal-header {
            background-color: #dc3545;
            color: white;
            text-align: center;
            padding: 1rem;
        }

        .modal-title {
            width: 100%;
            text-align: center;
            font-weight: bold;
            letter-spacing: 1px;
        }

        .btn-close {
            background-color: white;
            opacity: 0.8;
        }

        .btn-close:hover {
            opacity: 1;
        }

        .form-control:disabled,
        .form-control[readonly] {
            background-color: #e9ecef;
            opacity: 0.7;
        }

        optgroup {
            font-weight: bold;
            color: #dc3545;
        }

        option:disabled {
            color: #6c757d;
            font-style: italic;
        }

        .form-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #333;
        }

        #date, #time {
            font-size: 1rem;
            padding: 0.75rem;
            border-radius: 8px;
            border: 1px solid #ced4da;
            transition: border-color 0.2s ease-in-out;
        }

        #date:focus, #time:focus {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        #time option {
            padding: 8px;
        }

        #time optgroup {
            margin-top: 8px;
        }

        .included-services {
            list-style: none;
            padding-left: 15px;
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
        }

        .included-services li {
            margin-bottom: 5px;
        }

        .included-services .fas {
            color: #28a745;
        }

        .date-input {
            background-color: #1a1a1a;
            color: #ffffff;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 12px;
            font-size: 16px;
            width: 100%;
        }

        .date-input::-webkit-calendar-picker-indicator {
            filter: invert(1);
            cursor: pointer;
            padding: 5px;
            opacity: 0.8;
        }

        .date-input::-webkit-calendar-picker-indicator:hover {
            opacity: 1;
        }

        .date-input:focus {
            outline: none;
            border-color: #dc3545;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
        }

        .date-input::-webkit-datetime-edit-fields-wrapper {
            color: #ffffff;
        }
        
        .date-input::-webkit-datetime-edit-text {
            color: #ffffff;
            padding: 0 0.3em;
        }
        
        .date-input::-webkit-datetime-edit-month-field,
        .date-input::-webkit-datetime-edit-day-field,
        .date-input::-webkit-datetime-edit-year-field {
            color: #ffffff;
        }

        .date-input::-webkit-calendar-picker-indicator {
            background-color: transparent;
            padding: 5px;
            cursor: pointer;
            border-radius: 3px;
            filter: invert(1);
        }

        .date-input::-webkit-clear-button {
            display: none;
        }

        .date-input::-webkit-inner-spin-button {
            display: none;
        }

        /* Updated time selector styles */
        .time-select {
            background-color: #1a1a1a !important;
            color: #ffffff !important;
            border: 1px solid #333 !important;
            border-radius: 8px !important;
            padding: 12px !important;
            font-size: 16px !important;
            width: 100% !important;
            cursor: pointer !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
            appearance: none !important;
            background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23FFFFFF%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E") !important;
            background-repeat: no-repeat !important;
            background-position: right 12px top 50% !important;
            background-size: 12px auto !important;
            padding-right: 40px !important;
        }

        .time-select:focus {
            outline: none !important;
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25) !important;
        }

        .time-select option {
            background-color: #1a1a1a !important;
            color: #ffffff !important;
            padding: 12px !important;
        }

        .time-select option:disabled {
            color: #666666 !important;
            background-color: #1a1a1a !important;
        }

        .time-select optgroup {
            color: #dc3545 !important;
            font-weight: bold !important;
            padding: 8px !important;
            background-color: #1a1a1a !important;
        }

        .time-select::-ms-expand {
            display: none !important;
        }

        /* Live Banner Styles */
        #liveBanner {
            display: none;
            background-color: #ff0000;
            color: white;
            text-align: center;
            padding: 10px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            font-weight: bold;
            font-size: 1.1em;
        }

        #liveBanner a {
            color: white;
            text-decoration: underline;
        }

        #liveBanner a:hover {
            color: #f0f0f0;
        }

        /* Updated Logo Styles */
        .navbar-logo {
            width: 100px;  /* Increased for better visibility */
            height: 100px; /* Increased for better visibility */
            object-fit: contain;
            margin-right: 15px;
            border-radius: 10px;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            font-size: 1.4em;
            padding: 0;
        }

        .footer-logo {
            width: 100px;  /* Increased from default size */
            height: 100px; /* Increased from default size */
            object-fit: contain;
            border-radius: 10px;
        }

        /* Adjust navbar padding to accommodate larger logo */
        .navbar {
            padding: 0.5rem 1rem;
        }
        
        /* Ensure the navbar height adjusts to the logo */
        .navbar-brand {
            padding: 0;
        }
        
        @media (max-width: 768px) {
            .navbar-logo {
                width: 75px;  /* Increased mobile size for better visibility */
                height: 75px;
            }
        }
        
        /* Contact section styles */
        .contact-card {
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 25px 20px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            height: 100%;
        }
        
        .contact-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        
        .card-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 70px;
            height: 70px;
            background-color: #dc3545;
            border-radius: 50%;
            margin-bottom: 15px;
            color: white;
            font-size: 28px;
        }
        
        .map-container {
            height: 400px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .contact-form {
            background-color: rgba(0, 0, 0, 0.3);
            padding: 30px;
            border-radius: 10px;
            height: 100%;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
    </style>

    <!-- Live Banner -->
    <div id="liveBanner">
        🔴 LIVE NOW - Watch on <a href="#" id="liveLink" target="_blank">YouTube</a>
    </div>

    <!-- Social Media Live Cards Section -->
    <section id="live-streams" class="py-5">
        <div class="container">
            <h2 class="section-title text-center mb-5">BARBER BROTHERS LIVE</h2>
            <div class="row">
                <!-- YouTube Card -->
                <div class="col-md-4 mb-4">
                    <div class="stream-card">
                        <div class="stream-platform">
                            <i class="fab fa-youtube"></i> YouTube
                </div>
                        <div class="stream-status" id="youtubeStatus">
                            <span class="status-dot"></span>
                            <span class="status-text">Offline</span>
            </div>
                        <div class="stream-info">
                            <h4>Andre The Barber</h4>
                            <p>Watch live haircut tutorials, styling tips, and barber talk!</p>
                            <div class="stream-buttons">
                                <a href="https://www.youtube.com/channel/UCr_jZIzeLrJ4oGo6yFWsfzQ" target="_blank" class="btn btn-youtube mb-2">
                                    <i class="fab fa-youtube"></i> Visit Channel
                                </a>
                                <a href="https://studio.youtube.com" target="_blank" class="btn btn-outline-youtube">
                                    <i class="fas fa-video"></i> Go Live
                                </a>
        </div>
                        </div>
                        </div>
                    </div>
                <!-- Facebook Card -->
                <div class="col-md-4 mb-4">
                    <div class="stream-card">
                        <div class="stream-platform">
                            <i class="fab fa-facebook"></i> Facebook
                </div>
                        <div class="stream-status" id="facebookStatus">
                            <span class="status-dot"></span>
                            <span class="status-text">Offline</span>
                        </div>
                        <div class="stream-info">
                            <h4>Barber Brothers Legacy</h4>
                            <p>Join our live sessions for exclusive content and community interaction!</p>
                            <div class="stream-buttons">
                                <a href="https://www.facebook.com/profile.php?id=61557545061616" target="_blank" class="btn btn-facebook mb-2">
                                    <i class="fab fa-facebook"></i> Visit Page
                                </a>
                                <a href="https://www.facebook.com/live/producer" target="_blank" class="btn btn-outline-facebook">
                                    <i class="fas fa-video"></i> Go Live
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Instagram Card -->
                <div class="col-md-4 mb-4">
                    <div class="stream-card">
                        <div class="stream-platform">
                            <i class="fab fa-instagram"></i> Instagram
                        </div>
                        <div class="stream-status" id="instagramStatus">
                            <span class="status-dot"></span>
                            <span class="status-text">Offline</span>
                        </div>
                        <div class="stream-info">
                            <h4>@barbar_brothers_23</h4>
                            <p>Follow us for live stories, reels, and behind-the-scenes content!</p>
                            <div class="stream-buttons">
                                <a href="https://www.instagram.com/barbar_brothers_23/" target="_blank" class="btn btn-instagram mb-2">
                                    <i class="fab fa-instagram"></i> Visit Profile
                                </a>
                                <a href="https://www.instagram.com/barbar_brothers_23/live" target="_blank" class="btn btn-outline-instagram">
                                    <i class="fas fa-video"></i> Go Live
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
    /* Updated Live Stream Cards Styles */
    .stream-card {
        background: #1a1a1a;
        border-radius: 15px;
        padding: 25px;
        color: white;
        position: relative;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
        border: 2px solid #dc3545;
    }

    .stream-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.2);
    }

    .stream-platform {
        font-size: 1.2em;
        font-weight: bold;
        margin-bottom: 15px;
        color: #dc3545;
    }

    .stream-platform i {
        margin-right: 10px;
    }

    .stream-status {
        position: absolute;
        top: 25px;
        right: 25px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #666;
    }

    .status-dot.live {
        background: #dc3545;
        animation: pulse 2s infinite;
    }

    .stream-buttons {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .stream-buttons .btn {
        width: 100%;
        padding: 12px;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    /* Primary buttons */
    .stream-buttons .btn-youtube,
    .stream-buttons .btn-facebook,
    .stream-buttons .btn-instagram {
        background: #dc3545;
        color: white;
        border: none;
    }

    .stream-buttons .btn-youtube:hover,
    .stream-buttons .btn-facebook:hover,
    .stream-buttons .btn-instagram:hover {
        background: #c82333;
        color: white;
    }

    /* Outline buttons */
    .stream-buttons .btn-outline-youtube,
    .stream-buttons .btn-outline-facebook,
    .stream-buttons .btn-outline-instagram {
        color: #dc3545;
        border: 2px solid #dc3545;
        background: transparent;
    }

    .stream-buttons .btn-outline-youtube:hover,
    .stream-buttons .btn-outline-facebook:hover,
    .stream-buttons .btn-outline-instagram:hover {
        background: #dc3545;
        color: white;
    }

    .stream-info .btn i {
        margin-right: 8px;
    }

    /* Live Banner update */
    #liveBanner {
        display: none;
        background-color: #dc3545;
        color: white;
        text-align: center;
        padding: 10px;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        font-weight: bold;
        font-size: 1.1em;
    }
    </style>

    <!-- Social Media Feed -->
    <section id="social-feed" class="py-5 bg-dark text-white">
        <div class="container">
            <h2 class="section-title text-center mb-5">BARBER BROTHERS MEDIA</h2>
            <div class="row justify-content-center">
                <div class="col-lg-8 col-md-10">
                    <!-- Profile Header -->
                    <div class="social-profile-header mb-4 p-3 bg-black bg-opacity-50 rounded">
                        <div class="d-flex align-items-center">
                            <img src="images/time.jpeg" alt="Barber Brothers Media" class="profile-pic me-3" width="60" height="60">
                            <div>
                                <h4 class="mb-0">Barber Brothers Media</h4>
                                <p class="text-muted mb-0">@barberbrothers</p>
                                <div class="social-stats mt-2">
                                    <span><strong>523</strong> Posts</span>
                                    <span class="mx-3"><strong>12.4K</strong> Followers</span>
                                    <span><strong>856</strong> Following</span>
                                </div>
                            </div>
                            <div class="ms-auto">
                                <button class="btn btn-outline-danger">Follow</button>
                            </div>
                        </div>
                        <p class="mt-3">Showcasing the best in barber culture, style, and community. #BarberBrothersCuts #FreshStyles</p>
                    </div>
                    
                    <!-- Login Section (shown when not logged in) -->
                    <div id="login-section" class="text-center mb-4 p-4 bg-black bg-opacity-50 rounded">
                        <h3 class="mb-3">Join the Barber Brothers Community</h3>
                        <p class="mb-4">Sign in to share your style, comment on posts, and connect with other barber enthusiasts.</p>
                        <button id="google-signin-btn-2" class="btn btn-danger btn-lg w-100 mb-3">
                            <i class="fas fa-user me-2"></i> Sign In
                        </button>
                    </div>
                    
                    <!-- Create Post (hidden until logged in) -->
                    <div id="post-section" class="create-post mb-4 d-none">
                        <div class="card bg-black text-white">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <img src="images/time.jpeg" alt="User" class="user-avatar me-2" width="40" height="40">
                                    <input type="text" class="form-control bg-dark text-white" placeholder="What's on your mind?">
                                </div>
                                <div class="d-flex post-actions gap-2">
                                    <button class="btn btn-outline-light flex-fill"><i class="fas fa-image me-1"></i>Photo</button>
                                    <button class="btn btn-outline-light flex-fill"><i class="fas fa-video me-1"></i>Video</button>
                                    <button class="btn btn-danger flex-fill">Post</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Posts Feed -->
                    <div id="feed-section" class="posts-feed">
                        <!-- Post 1 -->
                        <div class="card social-post mb-4 bg-black text-white">
                            <div class="card-header post-header border-0 bg-black">
                                <div class="d-flex align-items-center">
                                    <img src="images/time.jpeg" alt="Barber Brothers Media" class="user-avatar me-2" width="40" height="40">
                                    <div>
                                        <h6 class="mb-0">Barber Brothers Media</h6>
                                        <small class="text-muted">Posted 2 hours ago</small>
                                    </div>
                                    <div class="dropdown ms-auto">
                                        <button class="btn btn-link text-white" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-dark">
                                            <li><a class="dropdown-item" href="#">Save Post</a></li>
                                            <li><a class="dropdown-item" href="#">Report</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body pb-2">
                                <p class="card-text">Just finished this fresh cut! What do you think? #FreshFades #BarberLife</p>
                                <img src="images/IMG_1073.jpeg" alt="Haircut" class="img-fluid rounded mb-3 post-image">
                                <div class="post-stats d-flex align-items-center justify-content-between text-muted mb-2">
                                    <div class="d-flex gap-3">
                                        <span><i class="fas fa-heart text-danger me-1"></i> 235</span>
                                        <span><i class="fas fa-comment me-1"></i> 42</span>
                                        <span><i class="fas fa-share me-1"></i> 18</span>
                                    </div>
                                    <small class="text-muted">2 hours ago</small>
                                </div>
                                <div class="post-actions d-flex justify-content-around">
                                    <button class="btn btn-link text-white px-3 py-2"><i class="far fa-heart me-1"></i> Like</button>
                                    <button class="btn btn-link text-white px-3 py-2"><i class="far fa-comment me-1"></i> Comment</button>
                                    <button class="btn btn-link text-white px-3 py-2"><i class="far fa-share-square me-1"></i> Share</button>
                                </div>
                            </div>
                        </div>

                        <!-- Post 2 -->
                        <div class="card social-post mb-4 bg-black text-white">
                            <div class="card-header post-header border-0 bg-black">
                                <div class="d-flex align-items-center">
                                    <img src="images/time.jpeg" alt="Barber Brothers Media" class="user-avatar me-2" width="40" height="40">
                                    <div>
                                        <h6 class="mb-0">Barber Brothers Media</h6>
                                        <small class="text-muted">Posted 1 day ago</small>
                                    </div>
                                    <div class="dropdown ms-auto">
                                        <button class="btn btn-link text-white" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-dark">
                                            <li><a class="dropdown-item" href="#">Save Post</a></li>
                                            <li><a class="dropdown-item" href="#">Report</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body pb-2">
                                <p class="card-text">Behind the scenes at Barber Brothers Legacy! Always perfecting our craft. 💯 #BarberLife #BehindTheScenes</p>
                                <img src="images/about.jpeg" alt="Behind the scenes" class="img-fluid rounded mb-3 post-image">
                                <div class="post-stats d-flex align-items-center justify-content-between text-muted mb-2">
                                    <div class="d-flex gap-3">
                                        <span><i class="fas fa-heart text-danger me-1"></i> 189</span>
                                        <span><i class="fas fa-comment me-1"></i> 28</span>
                                        <span><i class="fas fa-share me-1"></i> 12</span>
                                    </div>
                                    <small class="text-muted">1 day ago</small>
                                </div>
                                <div class="post-actions d-flex justify-content-around">
                                    <button class="btn btn-link text-white px-3 py-2"><i class="far fa-heart me-1"></i> Like</button>
                                    <button class="btn btn-link text-white px-3 py-2"><i class="far fa-comment me-1"></i> Comment</button>
                                    <button class="btn btn-link text-white px-3 py-2"><i class="far fa-share-square me-1"></i> Share</button>
                                </div>
                            </div>
                        </div>

                        <!-- Load More Button -->
                        <div class="text-center mt-4">
                            <button class="btn btn-outline-danger btn-lg px-4">
                                <i class="fas fa-plus me-2"></i>Load More Posts
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="py-5 bg-light">
        <div class="container">
            <h2 class="section-title text-center mb-5">WHAT OUR CLIENTS SAY</h2>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <p>"Andre is the best barber in town! He always gives me exactly what I want and takes his time to make sure everything is perfect."</p>
                        </div>
                        <div class="testimonial-info">
                            <h4>James Wilson</h4>
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <p>"My son loves getting his haircut at Barber Brothers Legacy. Andre makes him feel comfortable and the results are always amazing!"</p>
                        </div>
                        <div class="testimonial-info">
                            <h4>Michael Thomas</h4>
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <p>"The full service package is worth every penny! Great cuts, excellent service, and a relaxing atmosphere. Andre is a true professional."</p>
                        </div>
                        <div class="testimonial-info">
                            <h4>Robert Johnson</h4>
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Community Section -->
    <section id="community" class="py-5 bg-dark text-white">
        <div class="container">
            <h2 class="section-title text-center mb-5">
                <i class="fas fa-users me-2"></i>BARBER BROTHERS COMMUNITY
            </h2>

            <!-- Community Description -->
            <div class="row justify-content-center mb-5">
                <div class="col-lg-8 text-center">
                    <p class="lead mb-4">
                        Join our vibrant community of barbers and clients. Share your work, connect with others,
                        and stay updated with the latest trends in the barbering world.
                    </p>
                </div>
            </div>

            <!-- Community Hub Container -->
            <div id="community-feed-root" class="community-hub-wrapper">
                <!-- React components will mount here -->
                <div class="text-center py-5">
                    <div class="spinner-border text-danger" role="status">
                        <span class="visually-hidden">Loading community...</span>
                    </div>
                    <p class="mt-3">Loading community hub...</p>
                </div>
            </div>

            <!-- Community Features Preview (shown while loading) -->
            <div class="row mt-5" id="community-features">
                <div class="col-md-4 mb-4">
                    <div class="community-feature-card text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-camera fa-3x text-danger"></i>
                        </div>
                        <h4>Share Your Work</h4>
                        <p>Showcase your latest cuts and styling work to inspire others in the community.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="community-feature-card text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-comments fa-3x text-danger"></i>
                        </div>
                        <h4>Connect & Chat</h4>
                        <p>Engage with fellow barbers and clients through comments and discussions.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="community-feature-card text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-star fa-3x text-danger"></i>
                        </div>
                        <h4>Get Inspired</h4>
                        <p>Discover new techniques, trends, and styles from the barbering community.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-5 bg-dark text-white">
        <div class="container">
            <h2 class="section-title text-center mb-5">CONTACT US</h2>
            
            <!-- Contact Info Cards -->
            <div class="row mb-5">
                <div class="col-md-4 mb-4">
                    <div class="contact-card h-100">
                        <div class="card-icon"><i class="fas fa-map-marker-alt"></i></div>
                        <h3>Our Location</h3>
                        <p>6239 Fairburn Rd., <br>Douglasville, GA 30134</p>
                        <a href="https://maps.google.com/?q=6239+Fairburn+Rd,+Douglasville,+GA+30134" target="_blank" class="btn btn-outline-danger btn-sm mt-2">Get Directions</a>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="contact-card h-100">
                        <div class="card-icon"><i class="fas fa-clock"></i></div>
                        <h3>Working Hours</h3>
                        <p>Monday - Saturday: <br><strong>9am - 7pm</strong></p>
                        <p>Sunday: <strong>Closed</strong></p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="contact-card h-100">
                        <div class="card-icon"><i class="fas fa-phone"></i></div>
                        <h3>Get in Touch</h3>
                        <p><a href="tel:4043091971" class="text-white">(*************</a></p>
                        <div class="social-icons mt-3">
                            <a href="https://www.facebook.com/profile.php?id=61557545061616" target="_blank" class="social-icon facebook" aria-label="Facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://www.instagram.com/barbar_brothers_23/" target="_blank" class="social-icon instagram" aria-label="Instagram">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="https://www.youtube.com/channel/UCr_jZIzeLrJ4oGo6yFWsfzQ" target="_blank" class="social-icon youtube" aria-label="YouTube">
                                <i class="fab fa-youtube"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <!-- Google Map -->
                <div class="col-lg-6 mb-4">
                    <div class="map-container">
                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3316.2306739676246!2d-84.7743492!3d33.7518775!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x88f529a93e8c2801%3A0xa2a453642baa16bc!2s6239%20Fairburn%20Rd%2C%20Douglasville%2C%20GA%2030134!5e0!3m2!1sen!2sus!4v1715380799126!5m2!1sen!2sus" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                    </div>
                </div>
                
                <!-- Appointment Form -->
                <div class="col-lg-6">
                    <div class="contact-form">
                        <h3 class="mb-4">Book Your Appointment</h3>
                        <form id="appointmentForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <input type="text" class="form-control" id="name" placeholder="Your Name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <input type="tel" class="form-control" id="phone" placeholder="Your Phone" required>
                            </div>
                        </div>
                            <div class="mb-3">
                                <select class="form-control custom-select" id="service" required>
                                    <option value="">Select Service</option>
                                    <optgroup label="Individual Services">
                                        <option value="Adult Haircut">Adult Haircut ($40+)</option>
                                        <option value="Kids Haircut">Kids Haircut ($30+)</option>
                                        <option value="Full Service">Full Service ($60+)</option>
                                    </optgroup>
                                    <optgroup label="Combined Services">
                                        <!-- Dynamic options will be added here -->
                                    </optgroup>
                                </select>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="date" class="form-label">Select Date</label>
                                    <input type="date" class="form-control date-input" id="date" required min="" max="">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="time" class="form-label">Select Time</label>
                                    <select class="form-control time-select" id="time" required>
                                        <option value="" disabled selected>Choose a time...</option>
                                        <!-- Morning slots -->
                                        <optgroup label="Morning">
                                            <option value="9:00 AM">9:00 AM</option>
                                            <option value="9:15 AM">9:15 AM</option>
                                            <option value="9:30 AM">9:30 AM</option>
                                            <option value="9:45 AM">9:45 AM</option>
                                            <option value="10:00 AM">10:00 AM</option>
                                            <option value="10:15 AM">10:15 AM</option>
                                            <option value="10:30 AM">10:30 AM</option>
                                            <option value="10:45 AM">10:45 AM</option>
                                            <option value="11:00 AM">11:00 AM</option>
                                            <option value="11:15 AM">11:15 AM</option>
                                            <option value="11:30 AM">11:30 AM</option>
                                            <option value="11:45 AM">11:45 AM</option>
                                        </optgroup>
                                        <!-- Afternoon slots -->
                                        <optgroup label="Afternoon">
                                            <option value="12:00 PM">12:00 PM</option>
                                            <option value="12:15 PM">12:15 PM</option>
                                            <option value="12:30 PM">12:30 PM</option>
                                            <option value="12:45 PM">12:45 PM</option>
                                            <option value="1:00 PM">1:00 PM</option>
                                            <option value="1:15 PM">1:15 PM</option>
                                            <option value="1:30 PM">1:30 PM</option>
                                            <option value="1:45 PM">1:45 PM</option>
                                            <option value="2:00 PM">2:00 PM</option>
                                            <option value="2:15 PM">2:15 PM</option>
                                            <option value="2:30 PM">2:30 PM</option>
                                            <option value="2:45 PM">2:45 PM</option>
                                            <option value="3:00 PM">3:00 PM</option>
                                            <option value="3:15 PM">3:15 PM</option>
                                            <option value="3:30 PM">3:30 PM</option>
                                            <option value="3:45 PM">3:45 PM</option>
                                            <option value="4:00 PM">4:00 PM</option>
                                            <option value="4:15 PM">4:15 PM</option>
                                            <option value="4:30 PM">4:30 PM</option>
                                            <option value="4:45 PM">4:45 PM</option>
                                            <option value="5:00 PM">5:00 PM</option>
                                            <option value="5:15 PM">5:15 PM</option>
                                            <option value="5:30 PM">5:30 PM</option>
                                            <option value="5:45 PM">5:45 PM</option>
                                            <option value="6:00 PM">6:00 PM</option>
                                            <option value="6:15 PM">6:15 PM</option>
                                            <option value="6:30 PM">6:30 PM</option>
                                        </optgroup>
                                    </select>
                                </div>
                            </div>
                            <div class="mb-3">
                                <textarea class="form-control form-control-lg" id="notes" rows="2" placeholder="Special requests or notes"></textarea>
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="smsConsent" required>
                                <label class="form-check-label" for="smsConsent">
                                    I agree to receive appointment confirmations, reminders, and updates via SMS text messages. Message and data rates may apply. By checking this box, I confirm that I am the owner of this phone number and consent to receive automated text messages from Barber Brothers Legacy. You can opt out at any time by replying STOP.
                                </label>
                            </div>
                            <button type="submit" class="btn btn-danger btn-lg w-100">Book Now</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <img src="images/time.jpeg" alt="Barber Brothers Legacy Logo - Vintage Clock with Scissors" class="footer-logo me-3">
                        <div>
                            <h3>BARBER BROTHERS LEGACY</h3>
                            <p>Premium cuts by Andre The Barber</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>&copy; 2023 Barber Brothers Legacy. All rights reserved.</p>
                    <p>Designed with <i class="fas fa-heart"></i> for quality service</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://apis.google.com/js/api.js"></script>

    <!-- React Scripts -->
    <script src="https://unpkg.com/react@18/umd/react.development.js" crossorigin></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js" crossorigin></script>

    <!-- Firebase v10 Integration - Clean setup -->
    <script type="module">
        // Import the functions you need from the SDKs you need
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.14.1/firebase-app.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/10.14.1/firebase-analytics.js";
        // TODO: Add SDKs for Firebase products that you want to use
        // https://firebase.google.com/docs/web/setup#available-libraries

        // Your web app's Firebase configuration
        // For Firebase JS SDK v7.20.0 and later, measurementId is optional
        const firebaseConfig = {
            apiKey: "AIzaSyBLFm5Kmdw4yA6fHqB1YFgzrJPmzE46CNM",
            authDomain: "barberbrothers-45d06.firebaseapp.com",
            projectId: "barberbrothers-45d06",
            storageBucket: "barberbrothers-45d06.firebasestorage.app",
            messagingSenderId: "1064307847156",
            appId: "1:1064307847156:web:5eed1300bd16839804d3a1",
            measurementId: "G-MHG8DQY38J"
        };

        // Initialize Firebase only if not already initialized
        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
            console.log('✅ Firebase initialized with new configuration');
        } else {
            console.log('✅ Firebase already initialized');
        }

        // Mock GOOGLE_AUTH_CONFIG for compatibility
        window.GOOGLE_AUTH_CONFIG = {
            clientId: 'firebase-managed',
            redirectUri: `${window.location.origin}/auth-callback.html`,
            scopes: ['openid', 'email', 'profile']
        };

        // Mock functions for compatibility
        window.getGoogleAuthUrl = function() {
            return '#firebase-auth';
        };

        window.debugGoogleAuthConfig = function() {
            console.log('🔧 Using Firebase Auth (not legacy OAuth)');
        };
    </script>

    <!-- Application Scripts -->
    <script src="js/main.js"></script>
    <script src="js/service-calculator.js"></script>
    <script src="js/performance-monitor.js"></script>

    <!-- Community Hub Integration -->
    <script src="community-hub/main.js" type="module"></script>

    <!-- AI Voice Agent Integration -->
    <script src="js/voice-agent-integration.js"></script>
    <script>
    let config = null;

    // Initialize APIs
    async function initAPIs() {
        try {
            // Load configuration
            if (typeof getConfig === 'function') {
                config = await getConfig();

                if (config && config.youtubeApiKey) {
                    // Initialize YouTube API
                    await new Promise((resolve, reject) => {
                        gapi.load('client', async () => {
                            try {
                                await gapi.client.init({
                                    apiKey: config.youtubeApiKey,
                                    discoveryDocs: ['https://www.googleapis.com/discovery/v1/apis/youtube/v3/rest']
                                });
                                console.log('YouTube API initialized successfully');
                                resolve();
                            } catch (error) {
                                console.error('Error initializing YouTube API:', error);
                                resolve(); // Don't reject, just continue without API
                            }
                        });
                    });

                    // Start checking live status
                    await checkLiveStatus();
                    // Check every minute
                    setInterval(checkLiveStatus, 60000);
                } else {
                    console.warn('YouTube API configuration not available');
                }
            } else {
                console.warn('getConfig function not available');
            }
        } catch (error) {
            console.error('Error in initAPIs:', error);
        }
    }

    // Check if channel is live
    async function checkLiveStatus() {
        if (!config || !gapi || !gapi.client || !gapi.client.youtube) {
            console.log('YouTube API not available for live status check');
            return;
        }

        try {
            // Check YouTube live status
            const ytResponse = await gapi.client.youtube.search.list({
                part: 'snippet',
                channelId: config.youtubeChannelId,
                type: 'video',
                eventType: 'live'
            });

            const isYoutubeLive = ytResponse.result.items.length > 0;
            const liveBanner = document.getElementById('liveBanner');
            const youtubeStatus = document.getElementById('youtubeStatus');

            if (youtubeStatus) {
                const youtubeDot = youtubeStatus.querySelector('.status-dot');
                const youtubeText = youtubeStatus.querySelector('.status-text');

                if (isYoutubeLive) {
                    console.log('YouTube channel is live!');
                    if (liveBanner) liveBanner.style.display = 'block';
                    if (youtubeDot) youtubeDot.classList.add('live');
                    if (youtubeText) youtubeText.textContent = 'Live Now!';

                    // Update live link
                    const liveVideo = ytResponse.result.items[0];
                    const liveLink = document.getElementById('liveLink');
                    if (liveLink) liveLink.href = `https://youtube.com/watch?v=${liveVideo.id.videoId}`;
                } else {
                    if (youtubeDot) youtubeDot.classList.remove('live');
                    if (youtubeText) youtubeText.textContent = 'Offline';
                    if (liveBanner) liveBanner.style.display = 'none';
                }
            }

            // Facebook live status would be handled here if Facebook API is available
            const facebookStatus = document.getElementById('facebookStatus');
            if (facebookStatus) {
                const facebookDot = facebookStatus.querySelector('.status-dot');
                const facebookText = facebookStatus.querySelector('.status-text');

                // For now, we'll just show offline status for Facebook
                // This can be updated when Facebook API integration is added
                if (facebookDot) facebookDot.classList.remove('live');
                if (facebookText) facebookText.textContent = 'Offline';
            }

            // Instagram live status would be handled here if Instagram API is available
            const instagramStatus = document.getElementById('instagramStatus');
            if (instagramStatus) {
                const instagramDot = instagramStatus.querySelector('.status-dot');
                const instagramText = instagramStatus.querySelector('.status-text');

                // For now, we'll just show offline status for Instagram
                // This can be updated when Instagram API integration is added
                if (instagramDot) instagramDot.classList.remove('live');
                if (instagramText) instagramText.textContent = 'Offline';
            }

        } catch (error) {
            console.error('Error checking live status:', error);
        }
    }

    // Initialize when the page loads
    document.addEventListener('DOMContentLoaded', () => {
        // Initialize APIs in the background, don't block other functionality
        setTimeout(() => {
            initAPIs();
        }, 1000);
    });

    

    // Handle authentication state on page load
    function handleAuthenticationState() {
        const authSuccess = localStorage.getItem('authSuccess');
        const userProfile = localStorage.getItem('userProfile');

        if (authSuccess === 'true' && userProfile) {
            try {
                const user = JSON.parse(userProfile);
                console.log('User authenticated:', user);
                updateUIForAuthenticatedUser(user);

                // Clear the auth success flag but keep user profile
                localStorage.removeItem('authSuccess');
            } catch (error) {
                console.error('Error parsing user profile:', error);
                localStorage.removeItem('authSuccess');
                localStorage.removeItem('userProfile');
            }
        }
    }

    // Update UI for authenticated user
    function updateUIForAuthenticatedUser(user) {
        // Update navigation
        const signinMenu = document.getElementById('signin-menu');
        const userMenu = document.getElementById('user-menu');

        if (signinMenu) signinMenu.classList.add('d-none');
        if (userMenu) {
            userMenu.classList.remove('d-none');

            // Update user avatar and name
            const userAvatar = document.getElementById('user-avatar');
            const userName = document.getElementById('user-name');

            if (userAvatar) userAvatar.src = user.picture || 'images/time.jpeg';
            if (userName) userName.textContent = user.name || 'User';
        }

        // Update social media section
        updateSocialMediaSection(user);

        // Show success message
        showAuthSuccessMessage(user.name);
    }

    // Update social media section for authenticated user
    function updateSocialMediaSection(user) {
        const loginSection = document.getElementById('login-section');
        const postSection = document.getElementById('post-section');

        if (loginSection) loginSection.classList.add('d-none');
        if (postSection) {
            postSection.classList.remove('d-none');

            // Update user avatar in post section
            const postAvatar = postSection.querySelector('.user-avatar');
            if (postAvatar) postAvatar.src = user.picture || 'images/time.jpeg';
        }

        // Update participate button to show user is signed in
        const participateBtn = document.querySelector('button[onclick*="showSignInModal()"]');
        if (participateBtn) {
            participateBtn.innerHTML = '<i class="fas fa-check me-2"></i> Signed In';
            participateBtn.classList.remove('btn-danger');
            participateBtn.classList.add('btn-success');
            participateBtn.onclick = null;
        }
    }

    // Show authentication success message
    function showAuthSuccessMessage(userName) {
        // Create and show a toast notification
        const toastContainer = document.getElementById('toast-container') || createToastContainer();

        const toast = document.createElement('div');
        toast.className = 'toast show';
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="toast-header">
                <strong class="me-auto text-success">Welcome!</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                Successfully signed in as ${userName}. You can now participate in the Barber Brothers community!
            </div>
        `;

        toastContainer.appendChild(toast);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }

    // Create toast container if it doesn't exist
    function createToastContainer() {
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
        return container;
    }

    // Create a toast notification
    function createToast(type, title, message) {
        const toastId = 'toast-' + Date.now();
        const iconMap = {
            'success': 'fas fa-check-circle text-success',
            'error': 'fas fa-exclamation-circle text-danger',
            'warning': 'fas fa-exclamation-triangle text-warning',
            'info': 'fas fa-info-circle text-info'
        };

        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.id = toastId;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        toast.innerHTML = `
            <div class="toast-header">
                <i class="${iconMap[type] || iconMap.info} me-2"></i>
                <strong class="me-auto">${title}</strong>
                <small>Just now</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;

        return toast;
    }

    // Show a toast notification
    function showToast(toast) {
        const container = createToastContainer();
        container.appendChild(toast);

        // Initialize Bootstrap toast
        if (typeof bootstrap !== 'undefined') {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        } else {
            // Fallback if Bootstrap is not loaded
            toast.classList.add('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 5000);
        }
    }

    // Handle sign out
    function handleSignOut() {
        localStorage.removeItem('userProfile');
        localStorage.removeItem('googleAuthData');
        localStorage.removeItem('authSuccess');

        // Reset UI
        const signinMenu = document.getElementById('signin-menu');
        const userMenu = document.getElementById('user-menu');
        const loginSection = document.getElementById('login-section');
        const postSection = document.getElementById('post-section');

        if (signinMenu) signinMenu.classList.remove('d-none');
        if (userMenu) userMenu.classList.add('d-none');
        if (loginSection) loginSection.classList.remove('d-none');
        if (postSection) postSection.classList.add('d-none');

        // Reset participate button
        const participateBtn = document.querySelector('.btn-success');
        if (participateBtn && participateBtn.innerHTML.includes('Signed In')) {
            participateBtn.innerHTML = '<i class="fab fa-google me-2"></i> Participate';
            participateBtn.classList.remove('btn-success');
            participateBtn.classList.add('btn-danger');
            participateBtn.onclick = () => showSignInModal();
        }

        window.location.reload();
    }

    // Add sign out event listener
    document.addEventListener('click', function(e) {
        if (e.target && e.target.id === 'sign-out-link') {
            e.preventDefault();
            handleSignOut();
        }
    });

    // Initialize authentication state on page load
    handleAuthenticationState();

    // Add debugging tools for OAuth troubleshooting
    function addOAuthDebugTools() {
        // Add debug info to console
        console.log('🔧 OAuth Debug Information:');
        console.log('📱 User Agent:', navigator.userAgent);
        console.log('🌐 Current URL:', window.location.href);
        console.log('📍 Origin:', window.location.origin);
        console.log('🔑 Client ID:', GOOGLE_AUTH_CONFIG?.clientId);
        console.log('🔗 Redirect URI:', GOOGLE_AUTH_CONFIG?.redirectUri);
        console.log('📋 Scopes:', GOOGLE_AUTH_CONFIG?.scopes);

        // Check if running on mobile
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        console.log('📱 Is Mobile:', isMobile);

        // Check if HTTPS
        const isHTTPS = window.location.protocol === 'https:';
        const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
        console.log('🔒 Is HTTPS:', isHTTPS);
        console.log('🏠 Is Localhost:', isLocalhost);

        if (!isHTTPS && !isLocalhost) {
            console.warn('⚠️ WARNING: OAuth requires HTTPS in production!');
        }
    }

    // Add debug button for testing (only in development)
    function addDebugButton() {
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            const debugBtn = document.createElement('button');
            debugBtn.innerHTML = '🔧 Debug OAuth';
            debugBtn.className = 'btn btn-warning position-fixed';
            debugBtn.style.bottom = '20px';
            debugBtn.style.left = '20px';
            debugBtn.style.zIndex = '9999';
            debugBtn.onclick = function() {
                addOAuthDebugTools();
                testOAuthConfiguration();
            };
            document.body.appendChild(debugBtn);
        }
    }

    // Test OAuth configuration
    function testOAuthConfiguration() {
        console.log('🧪 Testing OAuth Configuration...');

        try {
            // Test if config is loaded
            if (typeof GOOGLE_AUTH_CONFIG === 'undefined') {
                throw new Error('GOOGLE_AUTH_CONFIG not loaded');
            }

            // Test if function is available
            if (typeof getGoogleAuthUrl !== 'function') {
                throw new Error('getGoogleAuthUrl function not available');
            }

            // Test URL generation
            const testUrl = getGoogleAuthUrl('test');
            console.log('✅ Test OAuth URL generated:', testUrl);

            // Validate URL components
            const url = new URL(testUrl);
            const params = new URLSearchParams(url.search);

            console.log('🔍 URL Parameters:');
            console.log('  client_id:', params.get('client_id'));
            console.log('  redirect_uri:', params.get('redirect_uri'));
            console.log('  scope:', params.get('scope'));
            console.log('  response_type:', params.get('response_type'));
            console.log('  state:', params.get('state'));

            const toast = createToast('success', 'OAuth Test Passed', 'Configuration appears to be working correctly. Check console for details.');
            showToast(toast);

        } catch (error) {
            console.error('❌ OAuth Test Failed:', error);
            const toast = createToast('error', 'OAuth Test Failed', error.message);
            showToast(toast);
        }
    }

    // Initialize debug tools
    addOAuthDebugTools();
    addDebugButton();

</script>

<!-- Barber Brothers Media Scripts -->
<script src="js/social-media.js"></script>
<script src="js/social-media-firebase.js"></script>

<!-- Main JavaScript -->
<script src="js/main.js"></script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
