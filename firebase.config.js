/**
 * Firebase Configuration
 * Centralized Firebase setup for Barber Brothers Legacy
 * Supports both development and production environments
 */

// Import the functions you need from the SDKs you need
import { initializeApp, getApps } from "firebase/app";
import { getAuth, connectAuthEmulator } from "firebase/auth";
import { getFirestore, connectFirestoreEmulator } from "firebase/firestore";
import { getStorage, connectStorageEmulator } from "firebase/storage";
import { getAnalytics, isSupported } from "firebase/analytics";

// Browser-compatible configuration
const getFirebaseConfig = () => {
  // For browser environment, use the new Firebase config
  // These values match your new Firebase project settings
  const config = {
    apiKey: "AIzaSyBLFm5Kmdw4yA6fHqB1YFgzrJPmzE46CNM",
    authDomain: "barberbrothers-45d06.firebaseapp.com",
    projectId: "barberbrothers-45d06",
    storageBucket: "barberbrothers-45d06.firebasestorage.app",
    messagingSenderId: "1064307847156",
    appId: "1:1064307847156:web:5eed1300bd16839804d3a1",
    measurementId: "G-MHG8DQY38J"
  };

  return config;
};

// Initialize Firebase app (prevent multiple initialization)
let app;
const firebaseConfig = getFirebaseConfig();

if (!getApps().length) {
  app = initializeApp(firebaseConfig);
  console.log('✅ Firebase initialized successfully');
} else {
  app = getApps()[0];
  console.log('✅ Firebase app already initialized');
}

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

// Initialize Analytics (only in browser and if supported)
let analytics = null;
if (typeof window !== 'undefined') {
  isSupported().then((supported) => {
    if (supported) {
      analytics = getAnalytics(app);
      console.log('✅ Firebase Analytics initialized');
    }
  }).catch((error) => {
    console.warn('⚠️ Firebase Analytics not supported:', error);
  });
}

export { analytics };

// Development emulator setup
const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
const useEmulators = false; // Set to true if you want to use Firebase emulators

if (isDevelopment && useEmulators && typeof window !== 'undefined') {
  // Connect to emulators if in development mode
  try {
    connectAuthEmulator(auth, 'http://localhost:9099', { disableWarnings: true });
    connectFirestoreEmulator(db, 'localhost', 8080);
    connectStorageEmulator(storage, 'localhost', 9199);
    console.log('🔧 Connected to Firebase emulators');
  } catch (error) {
    console.warn('⚠️ Could not connect to Firebase emulators:', error);
  }
}

// Export the app instance
export default app;

// Export configuration for debugging
export const firebaseConfigDebug = {
  projectId: firebaseConfig.projectId,
  authDomain: firebaseConfig.authDomain,
  isDevelopment,
  useEmulators,
  timestamp: new Date().toISOString()
};